"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getVars = getVars;
const vars_db_1 = require("../vars.db");
async function getVars(httpRequest) {
    try {
        const vars = await vars_db_1.varsDb.getVars();
        return {
            statusCode: 200,
            body: {
                success: true,
                data: vars,
                count: vars.length
            }
        };
    }
    catch (error) {
        return {
            statusCode: 500,
            body: {
                success: false,
                error: "Internal server error",
                message: error.message
            }
        };
    }
}
