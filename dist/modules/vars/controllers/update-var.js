"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateVar = updateVar;
const vars_db_1 = require("../vars.db");
const vars_schemas_1 = require("../vars.schemas");
async function updateVar(httpRequest) {
    try {
        const { id } = httpRequest.params;
        const validatedData = vars_schemas_1.varUpdateDTO.parse(httpRequest.body);
        if (!id) {
            return {
                statusCode: 400,
                headers: { "Content-Type": "application/json" },
                body: {
                    success: false,
                    error: "Var ID is required",
                },
            };
        }
        const varData = await vars_db_1.varsDb.updateVar(id, validatedData);
        return {
            statusCode: 200,
            headers: { "Content-Type": "application/json" },
            body: {
                success: true,
                data: varData,
                message: "Var updated successfully",
            },
        };
    }
    catch (error) {
        if (error.name === "ZodError") {
            return {
                statusCode: 400,
                headers: { "Content-Type": "application/json" },
                body: {
                    success: false,
                    error: "Validation error",
                    details: error.errors,
                },
            };
        }
        if (error.code === "P2025") {
            return {
                statusCode: 404,
                headers: { "Content-Type": "application/json" },
                body: {
                    success: false,
                    error: "Var not found",
                },
            };
        }
        return {
            statusCode: 500,
            headers: { "Content-Type": "application/json" },
            body: {
                success: false,
                error: "Internal server error",
                message: error.message,
            },
        };
    }
}
