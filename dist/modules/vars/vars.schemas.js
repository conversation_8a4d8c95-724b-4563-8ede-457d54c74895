"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.varResponseDTO = exports.varUpdateDTO = exports.varCreateDTO = void 0;
const zod_1 = require("zod");
exports.varCreateDTO = zod_1.z.object({
    name: zod_1.z.string({ required_error: "Var name required" }).min(1),
    description: zod_1.z.string().optional(),
});
exports.varUpdateDTO = zod_1.z.object({
    name: zod_1.z.string().min(1).optional(),
    description: zod_1.z.string().optional(),
});
exports.varResponseDTO = zod_1.z.object({
    id: zod_1.z.string(),
    name: zod_1.z.string(),
    description: zod_1.z.string().nullable(),
    organizations: zod_1.z.array(zod_1.z.object({
        id: zod_1.z.string(),
        name: zod_1.z.string(),
        NAICSCode: zod_1.z.string(),
        yearsInBusiness: zod_1.z.number(),
        valuation: zod_1.z.number().nullable(),
    })).optional(),
});
