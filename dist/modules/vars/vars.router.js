"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.router = void 0;
const express_1 = require("express");
const express_callback_1 = require("../../middleware/express-callback");
const create_var_1 = require("./controllers/create-var");
const get_var_1 = require("./controllers/get-var");
const get_vars_1 = require("./controllers/get-vars");
const update_var_1 = require("./controllers/update-var");
const delete_var_1 = require("./controllers/delete-var");
exports.router = (0, express_1.Router)();
// Var routes
exports.router.post("/", (0, express_callback_1.makeExpressCallback)(create_var_1.createVar));
exports.router.get("/", (0, express_callback_1.makeExpressCallback)(get_vars_1.getVars));
exports.router.get("/:id", (0, express_callback_1.makeExpressCallback)(get_var_1.getVar));
exports.router.put("/:id", (0, express_callback_1.makeExpressCallback)(update_var_1.updateVar));
exports.router.delete("/:id", (0, express_callback_1.makeExpressCallback)(delete_var_1.deleteVar));
