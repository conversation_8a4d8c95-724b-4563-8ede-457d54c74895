"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getOrganizations = getOrganizations;
const organizations_db_1 = require("../organizations.db");
async function getOrganizations(httpRequest) {
    try {
        const organizations = await organizations_db_1.organizationsDb.getOrganizations();
        return {
            statusCode: 200,
            body: {
                success: true,
                data: organizations,
                count: organizations.length
            }
        };
    }
    catch (error) {
        return {
            statusCode: 500,
            body: {
                success: false,
                error: "Internal server error",
                message: error.message
            }
        };
    }
}
