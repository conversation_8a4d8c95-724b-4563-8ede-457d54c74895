"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createOrganization = createOrganization;
const organizations_db_1 = require("../organizations.db");
const organizations_schemas_1 = require("../organizations.schemas");
async function createOrganization(httpRequest) {
    try {
        const validatedData = organizations_schemas_1.organizationCreateDTO.parse(httpRequest.body);
        const organization = await organizations_db_1.organizationsDb.createOrganization({
            name: validatedData.name,
            NAICSCode: validatedData.NAICSCode,
            yearsInBusiness: validatedData.yearsInBusiness,
            valuation: validatedData.valuation,
            var: {
                connect: { id: validatedData.varId },
            },
        });
        return {
            statusCode: 201,
            headers: { "Content-Type": "application/json" },
            body: {
                success: true,
                data: organization,
                message: "Organization created successfully",
            },
        };
    }
    catch (error) {
        if (error.name === "ZodError") {
            return {
                statusCode: 400,
                headers: { "Content-Type": "application/json" },
                body: {
                    success: false,
                    error: "Validation error",
                    details: error.errors,
                },
            };
        }
        if (error.code === "P2002") {
            return {
                statusCode: 409,
                headers: { "Content-Type": "application/json" },
                body: {
                    success: false,
                    error: "Organization with this name already exists",
                },
            };
        }
        return {
            statusCode: 500,
            headers: { "Content-Type": "application/json" },
            body: {
                success: false,
                error: "Internal server error",
                message: error.message,
            },
        };
    }
}
