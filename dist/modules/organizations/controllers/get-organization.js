"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getOrganization = getOrganization;
const organizations_db_1 = require("../organizations.db");
async function getOrganization(httpRequest) {
    try {
        const { id } = httpRequest.params;
        if (!id) {
            return {
                statusCode: 400,
                headers: { "Content-Type": "application/json" },
                body: {
                    success: false,
                    error: "Organization ID is required",
                },
            };
        }
        const organization = await organizations_db_1.organizationsDb.getOrganization(id);
        if (!organization) {
            return {
                statusCode: 404,
                headers: { "Content-Type": "application/json" },
                body: {
                    success: false,
                    error: "Organization not found",
                },
            };
        }
        return {
            statusCode: 200,
            headers: { "Content-Type": "application/json" },
            body: {
                success: true,
                data: organization,
            },
        };
    }
    catch (error) {
        return {
            statusCode: 500,
            headers: { "Content-Type": "application/json" },
            body: {
                success: false,
                error: "Internal server error",
                message: error.message,
            },
        };
    }
}
