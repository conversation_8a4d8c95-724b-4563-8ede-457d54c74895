"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.organizationsDb = void 0;
const prisma_1 = __importDefault(require("../../prisma"));
exports.organizationsDb = Object.freeze({
    // Organization CRUD
    createOrganization,
    getOrganization,
    getOrganizations,
    updateOrganization,
    deleteOrganization,
});
// Organization operations
async function createOrganization(data) {
    return await prisma_1.default.organization.create({
        data,
        include: {
            yearData: {
                orderBy: { year: "desc" },
            },
        },
    });
}
async function getOrganization(id) {
    return await prisma_1.default.organization.findUnique({
        where: { id },
        include: {
            yearData: {
                orderBy: { year: "desc" },
            },
        },
    });
}
async function getOrganizations() {
    return await prisma_1.default.organization.findMany({
        include: {
            yearData: {
                orderBy: { year: "desc" },
            },
        },
        orderBy: { name: "asc" },
    });
}
async function updateOrganization(id, data) {
    return await prisma_1.default.organization.update({
        where: { id },
        data,
        include: {
            yearData: {
                orderBy: { year: "desc" },
            },
        },
    });
}
async function deleteOrganization(id) {
    return await prisma_1.default.organization.delete({
        where: { id },
    });
}
