"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.router = void 0;
const express_1 = require("express");
const express_callback_1 = require("../../middleware/express-callback");
const create_organization_1 = require("./controllers/create-organization");
const get_organization_1 = require("./controllers/get-organization");
const get_organizations_1 = require("./controllers/get-organizations");
const update_organization_1 = require("./controllers/update-organization");
const delete_organization_1 = require("./controllers/delete-organization");
exports.router = (0, express_1.Router)();
// Organization routes
exports.router.post("/", (0, express_callback_1.makeExpressCallback)(create_organization_1.createOrganization));
exports.router.get("/", (0, express_callback_1.makeExpressCallback)(get_organizations_1.getOrganizations));
exports.router.get("/:id", (0, express_callback_1.makeExpressCallback)(get_organization_1.getOrganization));
exports.router.put("/:id", (0, express_callback_1.makeExpressCallback)(update_organization_1.updateOrganization));
exports.router.delete("/:id", (0, express_callback_1.makeExpressCallback)(delete_organization_1.deleteOrganization));
