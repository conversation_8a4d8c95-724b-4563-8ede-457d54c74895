"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.router = void 0;
const express_1 = require("express");
const express_callback_1 = require("../../middleware/express-callback");
const post_response_1 = require("./controllers/post-response");
const post_bulk_responses_1 = require("./controllers/post-bulk-responses");
const get_response_1 = require("./controllers/get-response");
const get_responses_by_evaluation_1 = require("./controllers/get-responses-by-evaluation");
const get_all_responses_1 = require("./controllers/get-all-responses");
exports.router = (0, express_1.Router)();
// GET routes
exports.router.get("/", (0, express_callback_1.makeExpressCallback)(get_all_responses_1.getAllResponses));
exports.router.get("/evaluation/:evaluationId", (0, express_callback_1.makeExpressCallback)(get_responses_by_evaluation_1.getResponsesByEvaluation));
exports.router.get("/:id", (0, express_callback_1.makeExpressCallback)(get_response_1.getResponse));
// POST routes
exports.router.get("/:surveyName", (0, express_callback_1.makeExpressCallback)(post_response_1.postResponse)); // Legacy route
exports.router.post("/bulk", (0, express_callback_1.makeExpressCallback)(post_bulk_responses_1.postBulkResponses));
