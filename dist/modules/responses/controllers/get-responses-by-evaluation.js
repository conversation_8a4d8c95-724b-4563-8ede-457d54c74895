"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getResponsesByEvaluation = getResponsesByEvaluation;
const responses_db_1 = require("../responses.db");
async function getResponsesByEvaluation(httpRequest) {
    try {
        const { evaluationId } = httpRequest.params;
        if (!evaluationId) {
            return {
                statusCode: 400,
                headers: { "Content-Type": "application/json" },
                body: {
                    success: false,
                    error: "Evaluation ID is required"
                }
            };
        }
        const responses = await responses_db_1.responsesDb.getResponsesByEvaluation(evaluationId);
        return {
            statusCode: 200,
            headers: { "Content-Type": "application/json" },
            body: {
                success: true,
                data: responses,
                count: responses.length
            }
        };
    }
    catch (error) {
        return {
            statusCode: 500,
            headers: { "Content-Type": "application/json" },
            body: {
                success: false,
                error: "Internal server error",
                message: error.message
            }
        };
    }
}
