"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.postBulkResponses = postBulkResponses;
const responses_db_1 = require("../responses.db");
const responses_schemas_1 = require("../responses.schemas");
const auto_complete_evaluation_1 = require("../../evaluations/services/auto-complete-evaluation");
async function postBulkResponses(httpRequest) {
    try {
        const validatedData = responses_schemas_1.bulkResponsesDTO.parse(httpRequest.body);
        const { evaluationId, responses } = validatedData;
        // Process each response
        const results = [];
        const errors = [];
        for (let i = 0; i < responses.length; i++) {
            const responseData = responses[i];
            try {
                // Validate that either selectedOptionId or selectedOptionIds is provided
                if (!responseData.selectedOptionId &&
                    (!responseData.selectedOptionIds || responseData.selectedOptionIds.length === 0)) {
                    errors.push({
                        index: i,
                        surveyQuestionId: responseData.surveyQuestionId,
                        error: "Either selectedOptionId or selectedOptionIds must be provided",
                    });
                    continue;
                }
                // Validate that both are not provided
                if (responseData.selectedOptionId &&
                    responseData.selectedOptionIds &&
                    responseData.selectedOptionIds.length > 0) {
                    errors.push({
                        index: i,
                        surveyQuestionId: responseData.surveyQuestionId,
                        error: "Cannot provide both selectedOptionId and selectedOptionIds",
                    });
                    continue;
                }
                let response;
                if (responseData.responseId) {
                    // Update existing response
                    response = await responses_db_1.responsesDb.updateResponse(responseData.responseId, responseData.selectedOptionId, responseData.selectedOptionIds);
                }
                else {
                    // Create new response
                    response = await responses_db_1.responsesDb.createResponse(evaluationId, responseData.surveyQuestionId, responseData.selectedOptionId, responseData.selectedOptionIds);
                }
                results.push({
                    index: i,
                    surveyQuestionId: responseData.surveyQuestionId,
                    responseId: response.id,
                    success: true,
                });
            }
            catch (error) {
                errors.push({
                    index: i,
                    surveyQuestionId: responseData.surveyQuestionId,
                    error: error.message || "Unknown error occurred",
                });
            }
        }
        // Check if evaluation can be auto-completed
        let autoCompletionResult = null;
        if (results.length > 0) {
            try {
                autoCompletionResult = await (0, auto_complete_evaluation_1.autoCompleteEvaluationIfReady)(evaluationId);
            }
            catch (error) {
                console.warn("Auto-completion check failed:", error.message);
            }
        }
        // Determine response status
        const hasErrors = errors.length > 0;
        const hasSuccesses = results.length > 0;
        let statusCode = 200;
        if (!hasSuccesses && hasErrors) {
            statusCode = 400; // All failed
        }
        else if (hasSuccesses && hasErrors) {
            statusCode = 207; // Partial success (Multi-Status)
        }
        else if (hasSuccesses && !hasErrors) {
            statusCode = 201; // All succeeded
        }
        return {
            statusCode,
            headers: { "Content-Type": "application/json" },
            body: {
                success: !hasErrors || hasSuccesses,
                data: {
                    evaluationId,
                    totalRequested: responses.length,
                    successful: results.length,
                    failed: errors.length,
                    results,
                    errors: errors.length > 0 ? errors : undefined,
                    autoCompletion: autoCompletionResult?.wasCompleted
                        ? {
                            completed: true,
                            brandStrengthScore: autoCompletionResult.brandStrengthScore,
                        }
                        : autoCompletionResult?.error
                            ? {
                                completed: false,
                                reason: autoCompletionResult.error,
                            }
                            : undefined,
                },
                message: hasErrors && !hasSuccesses
                    ? "All responses failed to save"
                    : hasErrors && hasSuccesses
                        ? `${results.length} responses saved, ${errors.length} failed`
                        : "All responses saved successfully",
            },
        };
    }
    catch (error) {
        if (error.name === "ZodError") {
            return {
                statusCode: 400,
                headers: { "Content-Type": "application/json" },
                body: {
                    success: false,
                    error: "Validation error",
                    details: error.errors,
                },
            };
        }
        console.error("Error in bulk responses:", error);
        return {
            statusCode: 500,
            headers: { "Content-Type": "application/json" },
            body: {
                success: false,
                error: "Internal server error",
                message: error.message,
            },
        };
    }
}
