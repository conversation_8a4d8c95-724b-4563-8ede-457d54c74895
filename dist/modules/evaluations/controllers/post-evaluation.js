"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.postEvaluation = postEvaluation;
const evaluations_db_1 = require("../evaluations.db");
async function postEvaluation(httpRequest) {
    try {
        const { surveyId } = httpRequest.params;
        const data = await evaluations_db_1.evaluationsDb.postEvaluation(surveyId);
        return {
            headers: { "Content-Type": "application/json" },
            statusCode: 200,
            body: { data },
        };
    }
    catch (error) {
        return {
            headers: { "Content-Type": "application/json" },
            statusCode: 400,
            body: error,
        };
    }
}
