"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.completeEvaluation = completeEvaluation;
const evaluations_db_1 = require("../evaluations.db");
const brand_strength_calculator_1 = require("../services/brand-strength-calculator");
async function completeEvaluation(httpRequest) {
    try {
        const { evaluationId } = httpRequest.params;
        if (!evaluationId) {
            return {
                statusCode: 400,
                headers: { "Content-Type": "application/json" },
                body: {
                    success: false,
                    error: "Evaluation ID is required",
                },
            };
        }
        // Get the evaluation with all responses and question options
        const evaluation = await evaluations_db_1.evaluationsDb.getEvaluationWithResponses(evaluationId);
        if (!evaluation) {
            return {
                statusCode: 404,
                headers: { "Content-Type": "application/json" },
                body: {
                    success: false,
                    error: "Evaluation not found",
                },
            };
        }
        // Check if evaluation is already completed
        if (evaluation.brandStrengthScore !== null) {
            return {
                statusCode: 409,
                headers: { "Content-Type": "application/json" },
                body: {
                    success: false,
                    error: "Evaluation is already completed",
                    data: {
                        evaluationId: evaluation.id,
                        brandStrengthScore: evaluation.brandStrengthScore,
                    },
                },
            };
        }
        // Calculate brand strength score
        const brandStrengthScore = (0, brand_strength_calculator_1.calculateBrandStrengthScore)(evaluation.responses);
        // Update evaluation with calculated score
        const updatedEvaluation = await evaluations_db_1.evaluationsDb.updateEvaluationScore(evaluationId, brandStrengthScore);
        return {
            statusCode: 200,
            headers: { "Content-Type": "application/json" },
            body: {
                success: true,
                data: updatedEvaluation,
                message: "Evaluation completed successfully",
            },
        };
    }
    catch (error) {
        console.error("Error completing evaluation:", error);
        return {
            statusCode: 500,
            headers: { "Content-Type": "application/json" },
            body: {
                success: false,
                error: "Internal server error",
                message: error.message,
            },
        };
    }
}
