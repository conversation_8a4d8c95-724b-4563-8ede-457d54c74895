"use strict";
// Brand Strength Score calculation service
Object.defineProperty(exports, "__esModule", { value: true });
exports.calculateBrandStrengthScore = calculateBrandStrengthScore;
exports.validateSurveyCompletion = validateSurveyCompletion;
/**
 * Calculate Brand Strength Score based on survey responses
 * @param responses Array of responses with their selected options and question details
 * @returns Calculated brand strength score (0-1000 scale)
 */
function calculateBrandStrengthScore(responses) {
    let totalScore = 0;
    let totalPossibleScore = 0;
    for (const response of responses) {
        const { surveyQuestion, selectedOption, selectedOptions } = response;
        const { type, questionOption } = surveyQuestion;
        switch (type) {
            case "RADIO": {
                if (selectedOption) {
                    // Add the score for the selected option
                    totalScore += selectedOption.scoreIfSelected;
                    // Add scores for all unselected options
                    const unselectedOptions = questionOption.filter(opt => opt.id !== selectedOption.id);
                    for (const option of unselectedOptions) {
                        totalScore += option.scoreIfUnselected;
                    }
                }
                // Calculate maximum possible score for this question
                const maxScoreForQuestion = Math.max(...questionOption.map(opt => opt.scoreIfSelected +
                    questionOption
                        .filter(other => other.id !== opt.id)
                        .reduce((sum, other) => sum + other.scoreIfUnselected, 0)));
                totalPossibleScore += maxScoreForQuestion;
                break;
            }
            case "MULTISELECT": {
                if (selectedOptions && selectedOptions.length > 0) {
                    const selectedIds = new Set(selectedOptions.map(opt => opt.id));
                    // Add scores for selected options
                    for (const option of selectedOptions) {
                        totalScore += option.scoreIfSelected;
                    }
                    // Add scores for unselected options
                    const unselectedOptions = questionOption.filter(opt => !selectedIds.has(opt.id));
                    for (const option of unselectedOptions) {
                        totalScore += option.scoreIfUnselected;
                    }
                }
                // Calculate maximum possible score for this question
                // (assuming all options could be selected for maximum benefit)
                const maxScoreForQuestion = questionOption.reduce((sum, opt) => sum + Math.max(opt.scoreIfSelected, opt.scoreIfUnselected), 0);
                totalPossibleScore += maxScoreForQuestion;
                break;
            }
            default:
                console.warn(`Unknown question type: ${type}`);
        }
    }
    // Avoid division by zero
    if (totalPossibleScore === 0) {
        return 0;
    }
    // Calculate percentage and scale to 0-1000
    const percentage = totalScore / totalPossibleScore;
    const brandStrengthScore = Math.round(percentage * 1000);
    // Ensure score is within bounds
    return Math.max(0, Math.min(1000, brandStrengthScore));
}
/**
 * Validate that all required questions have been answered
 * @param responses Array of responses
 * @param requiredQuestionIds Array of question IDs that must be answered
 * @returns Object with validation result and missing questions
 */
function validateSurveyCompletion(responses, requiredQuestionIds) {
    const answeredQuestionIds = new Set(responses.map(r => r.surveyQuestionId));
    const missingQuestions = requiredQuestionIds.filter(id => !answeredQuestionIds.has(id));
    return {
        isComplete: missingQuestions.length === 0,
        missingQuestions
    };
}
