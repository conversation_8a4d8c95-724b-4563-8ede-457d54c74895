"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.autoCompleteEvaluationIfReady = autoCompleteEvaluationIfReady;
exports.checkEvaluationReadiness = checkEvaluationReadiness;
const evaluations_db_1 = require("../evaluations.db");
const brand_strength_calculator_1 = require("./brand-strength-calculator");
/**
 * Automatically complete an evaluation if all required questions are answered
 * This can be called after each response is submitted to check for completion
 * @param evaluationId The evaluation to check for completion
 * @returns Object indicating if evaluation was completed and the score
 */
async function autoCompleteEvaluationIfReady(evaluationId) {
    try {
        // Get the evaluation with all responses
        const evaluation = await evaluations_db_1.evaluationsDb.getEvaluationWithResponses(evaluationId);
        if (!evaluation) {
            return { wasCompleted: false, error: "Evaluation not found" };
        }
        // Skip if already completed
        if (evaluation.brandStrengthScore !== null) {
            return {
                wasCompleted: false,
                brandStrengthScore: evaluation.brandStrengthScore,
                error: "Evaluation already completed",
            };
        }
        // Get all required questions for this survey
        // Note: You may need to adjust this based on your survey structure
        // For now, we'll assume all questions in the survey are required
        const surveyQuestions = evaluation.survey.questions || [];
        const requiredQuestionIds = surveyQuestions.map(q => q.id);
        // Validate completion
        const validation = (0, brand_strength_calculator_1.validateSurveyCompletion)(evaluation.responses, requiredQuestionIds);
        if (!validation.isComplete) {
            return {
                wasCompleted: false,
                error: `Missing responses for ${validation.missingQuestions.length} questions`,
            };
        }
        // Calculate and save brand strength score
        const brandStrengthScore = (0, brand_strength_calculator_1.calculateBrandStrengthScore)(evaluation.responses);
        await evaluations_db_1.evaluationsDb.updateEvaluationScore(evaluationId, brandStrengthScore);
        return {
            wasCompleted: true,
            brandStrengthScore,
        };
    }
    catch (error) {
        console.error("Error in auto-complete evaluation:", error);
        return {
            wasCompleted: false,
            error: error.message,
        };
    }
}
/**
 * Check if an evaluation is ready for completion (all questions answered)
 * @param evaluationId The evaluation to check
 * @returns Object indicating readiness and missing questions
 */
async function checkEvaluationReadiness(evaluationId) {
    try {
        const evaluation = await evaluations_db_1.evaluationsDb.getEvaluationWithResponses(evaluationId);
        if (!evaluation) {
            return {
                isReady: false,
                missingQuestions: [],
                totalQuestions: 0,
                answeredQuestions: 0,
            };
        }
        const surveyQuestions = evaluation.survey.questions || [];
        const requiredQuestionIds = surveyQuestions.map(q => q.id);
        const validation = (0, brand_strength_calculator_1.validateSurveyCompletion)(evaluation.responses, requiredQuestionIds);
        return {
            isReady: validation.isComplete,
            missingQuestions: validation.missingQuestions,
            totalQuestions: requiredQuestionIds.length,
            answeredQuestions: requiredQuestionIds.length - validation.missingQuestions.length,
        };
    }
    catch (error) {
        console.error("Error checking evaluation readiness:", error);
        return {
            isReady: false,
            missingQuestions: [],
            totalQuestions: 0,
            answeredQuestions: 0,
        };
    }
}
