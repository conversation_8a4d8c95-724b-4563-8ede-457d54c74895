"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.bulkCreateYearData = bulkCreateYearData;
const year_data_db_1 = require("../year-data.db");
const year_data_schemas_1 = require("../year-data.schemas");
async function bulkCreateYearData(httpRequest) {
    try {
        const validatedData = year_data_schemas_1.bulkYearDataCreateDTO.parse(httpRequest.body);
        const result = await year_data_db_1.yearDataDb.bulkCreateYearData(validatedData.orgId, validatedData.yearData);
        return {
            statusCode: 201,
            body: {
                success: true,
                data: result,
                message: `${result.count} year data records created successfully`
            }
        };
    }
    catch (error) {
        if (error.name === 'ZodError') {
            return {
                statusCode: 400,
                body: {
                    success: false,
                    error: "Validation error",
                    details: error.errors
                }
            };
        }
        if (error.code === 'P2002') {
            return {
                statusCode: 409,
                body: {
                    success: false,
                    error: "Some year data already exists for this organization"
                }
            };
        }
        return {
            statusCode: 500,
            body: {
                success: false,
                error: "Internal server error",
                message: error.message
            }
        };
    }
}
