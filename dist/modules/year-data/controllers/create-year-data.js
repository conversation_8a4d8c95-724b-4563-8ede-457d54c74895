"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createYearData = createYearData;
const year_data_db_1 = require("../year-data.db");
const year_data_schemas_1 = require("../year-data.schemas");
async function createYearData(httpRequest) {
    try {
        const validatedData = year_data_schemas_1.yearDataCreateDTO.parse(httpRequest.body);
        const yearData = await year_data_db_1.yearDataDb.createYearData({
            year: validatedData.year,
            MVIC: validatedData.MVIC,
            EBITDA: validatedData.EBITDA,
            OPM: validatedData.OPM,
            netSales: validatedData.netSales,
            org: {
                connect: { id: validatedData.orgId },
            },
        });
        return {
            statusCode: 201,
            headers: { "Content-Type": "application/json" },
            body: {
                success: true,
                data: yearData,
                message: "Year data created successfully",
            },
        };
    }
    catch (error) {
        if (error.name === "ZodError") {
            return {
                statusCode: 400,
                headers: { "Content-Type": "application/json" },
                body: {
                    success: false,
                    error: "Validation error",
                    details: error.errors,
                },
            };
        }
        if (error.code === "P2002") {
            return {
                statusCode: 409,
                headers: { "Content-Type": "application/json" },
                body: {
                    success: false,
                    error: "Year data for this organization and year already exists",
                },
            };
        }
        return {
            statusCode: 500,
            headers: { "Content-Type": "application/json" },
            body: {
                success: false,
                error: "Internal server error",
                message: error.message,
            },
        };
    }
}
