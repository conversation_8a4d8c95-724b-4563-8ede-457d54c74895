"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getYearDataByOrg = getYearDataByOrg;
const year_data_db_1 = require("../year-data.db");
async function getYearDataByOrg(httpRequest) {
    try {
        const { orgId } = httpRequest.params;
        if (!orgId) {
            return {
                statusCode: 400,
                body: {
                    success: false,
                    error: "Organization ID is required"
                }
            };
        }
        const yearData = await year_data_db_1.yearDataDb.getYearDataByOrg(orgId);
        return {
            statusCode: 200,
            body: {
                success: true,
                data: yearData,
                count: yearData.length
            }
        };
    }
    catch (error) {
        return {
            statusCode: 500,
            body: {
                success: false,
                error: "Internal server error",
                message: error.message
            }
        };
    }
}
