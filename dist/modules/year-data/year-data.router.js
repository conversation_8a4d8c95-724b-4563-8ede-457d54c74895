"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.router = void 0;
const express_1 = require("express");
const express_callback_1 = require("../../middleware/express-callback");
const create_year_data_1 = require("./controllers/create-year-data");
const bulk_create_year_data_1 = require("./controllers/bulk-create-year-data");
const get_year_data_1 = require("./controllers/get-year-data");
const get_year_data_by_org_1 = require("./controllers/get-year-data-by-org");
const update_year_data_1 = require("./controllers/update-year-data");
const delete_year_data_1 = require("./controllers/delete-year-data");
exports.router = (0, express_1.Router)();
// Year data routes
exports.router.post("/", (0, express_callback_1.makeExpressCallback)(create_year_data_1.createYearData));
exports.router.post("/bulk", (0, express_callback_1.makeExpressCallback)(bulk_create_year_data_1.bulkCreateYearData));
exports.router.get("/:id", (0, express_callback_1.makeExpressCallback)(get_year_data_1.getYearData));
exports.router.get("/organization/:orgId", (0, express_callback_1.makeExpressCallback)(get_year_data_by_org_1.getYearDataByOrg));
exports.router.put("/:id", (0, express_callback_1.makeExpressCallback)(update_year_data_1.updateYearData));
exports.router.delete("/:id", (0, express_callback_1.makeExpressCallback)(delete_year_data_1.deleteYearData));
