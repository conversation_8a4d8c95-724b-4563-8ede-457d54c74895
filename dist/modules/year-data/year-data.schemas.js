"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.bulkYearDataCreateDTO = exports.yearDataResponseDTO = exports.yearDataUpdateDTO = exports.yearDataCreateDTO = void 0;
const zod_1 = require("zod");
exports.yearDataCreateDTO = zod_1.z.object({
    orgId: zod_1.z.string({ required_error: "Organization ID required" }).min(1),
    year: zod_1.z.number({ required_error: "Year required" }).int().min(1900).max(2100),
    MVIC: zod_1.z.number({ required_error: "MVIC required" }),
    EBITDA: zod_1.z.number({ required_error: "EBITDA required" }),
    OPM: zod_1.z.number({ required_error: "OPM required" }),
    netSales: zod_1.z.number({ required_error: "Net Sales required" }),
});
exports.yearDataUpdateDTO = zod_1.z.object({
    year: zod_1.z.number().int().min(1900).max(2100).optional(),
    MVIC: zod_1.z.number().optional(),
    EBITDA: zod_1.z.number().optional(),
    OPM: zod_1.z.number().optional(),
    netSales: zod_1.z.number().optional(),
});
exports.yearDataResponseDTO = zod_1.z.object({
    id: zod_1.z.string(),
    orgId: zod_1.z.string(),
    year: zod_1.z.number(),
    MVIC: zod_1.z.number(),
    EBITDA: zod_1.z.number(),
    OPM: zod_1.z.number(),
    netSales: zod_1.z.number(),
});
exports.bulkYearDataCreateDTO = zod_1.z.object({
    orgId: zod_1.z.string({ required_error: "Organization ID required" }).min(1),
    yearData: zod_1.z.array(zod_1.z.object({
        year: zod_1.z.number().int().min(1900).max(2100),
        MVIC: zod_1.z.number(),
        EBITDA: zod_1.z.number(),
        OPM: zod_1.z.number(),
        netSales: zod_1.z.number(),
    })).min(1, "At least one year of data required"),
});
