"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getSurvey = getSurvey;
const surveys_db_1 = require("../surveys.db");
async function getSurvey(httpRequest) {
    try {
        const { surveyName } = httpRequest.params;
        const data = await surveys_db_1.surveysDb.getSurveyByName(surveyName);
        return {
            headers: { "Content-Type": "application/json" },
            statusCode: 200,
            body: { data },
        };
    }
    catch (error) {
        return {
            headers: { "Content-Type": "application/json" },
            statusCode: 400,
            body: error,
        };
    }
}
