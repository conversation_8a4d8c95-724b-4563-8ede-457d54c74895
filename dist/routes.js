"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const responses_router_1 = require("./modules/responses/responses.router");
const surveys_router_1 = require("./modules/surveys/surveys.router");
const evaluations_router_1 = require("./modules/evaluations/evaluations.router");
const organizations_router_1 = require("./modules/organizations/organizations.router");
const year_data_router_1 = require("./modules/year-data/year-data.router");
const vars_router_1 = require("./modules/vars/vars.router");
const routes = (0, express_1.Router)();
routes.use("/surveys", surveys_router_1.router);
routes.use("/responses", responses_router_1.router);
routes.use("/evaluations", evaluations_router_1.router);
routes.use("/organizations", organizations_router_1.router);
routes.use("/year-data", year_data_router_1.router);
routes.use("/vars", vars_router_1.router);
exports.default = routes;
