"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const routes_1 = __importDefault(require("./routes"));
const apply_middleware_1 = require("./middleware/apply-middleware");
const app = (0, express_1.default)();
(0, apply_middleware_1.applyMiddleware)(app);
app.use("/api", routes_1.default);
exports.default = app;
