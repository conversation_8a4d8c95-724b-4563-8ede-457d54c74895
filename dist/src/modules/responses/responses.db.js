"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.responsesDb = void 0;
const prisma_1 = __importDefault(require("../../prisma"));
exports.responsesDb = Object.freeze({
    postResponse: postResponseUpsert,
    createResponse,
    updateResponse,
    getResponse,
    getResponsesByEvaluation,
    getAllResponses,
});
// Legacy function for backward compatibility
async function postResponseUpsert(args) {
    return await prisma_1.default.response.upsert({ ...args });
}
// Create a new response
async function createResponse(evaluationId, surveyQuestionId, selectedOptionId, selectedOptionIds, freeResponseText) {
    const data = {
        evaluation: { connect: { id: evaluationId } },
        surveyQuestion: { connect: { id: surveyQuestionId } },
    };
    // Handle single option selection
    if (selectedOptionId) {
        data.selectedOption = { connect: { id: selectedOptionId } };
    }
    // Handle multiple option selection
    if (selectedOptionIds && selectedOptionIds.length > 0) {
        data.selectedOptions = {
            connect: selectedOptionIds.map(id => ({ id })),
        };
    }
    // Handle free response text
    if (freeResponseText !== undefined) {
        data.freeResponseText = freeResponseText;
    }
    return await prisma_1.default.response.create({ data });
}
// Update an existing response
async function updateResponse(responseId, selectedOptionId, selectedOptionIds, freeResponseText) {
    const data = {};
    // Handle single option selection
    if (selectedOptionId) {
        data.selectedOption = { connect: { id: selectedOptionId } };
        // Disconnect any existing multiple selections
        data.selectedOptions = { set: [] };
        // Clear free response text when selecting options
        data.freeResponseText = null;
    }
    // Handle multiple option selection
    if (selectedOptionIds && selectedOptionIds.length > 0) {
        data.selectedOptions = {
            set: selectedOptionIds.map(id => ({ id })),
        };
        // Disconnect any existing single selection
        data.selectedOption = { disconnect: true };
        // Clear free response text when selecting options
        data.freeResponseText = null;
    }
    // Handle free response text
    if (freeResponseText !== undefined) {
        data.freeResponseText = freeResponseText;
        // Clear option selections when providing free text
        data.selectedOption = { disconnect: true };
        data.selectedOptions = { set: [] };
    }
    // If nothing is provided, clear all responses
    if (!selectedOptionId &&
        (!selectedOptionIds || selectedOptionIds.length === 0) &&
        freeResponseText === undefined) {
        data.selectedOption = { disconnect: true };
        data.selectedOptions = { set: [] };
        data.freeResponseText = null;
    }
    return await prisma_1.default.response.update({
        where: { id: responseId },
        data,
    });
}
// Get a single response by ID
async function getResponse(responseId) {
    return await prisma_1.default.response.findUnique({
        where: { id: responseId },
        include: {
            surveyQuestion: {
                include: {
                    questionOption: true,
                },
            },
            selectedOption: true,
            selectedOptions: true,
            evaluation: {
                include: {
                    survey: true,
                    organization: true,
                },
            },
        },
    });
}
// Get all responses for a specific evaluation
async function getResponsesByEvaluation(evaluationId) {
    return await prisma_1.default.response.findMany({
        where: { evaluationId },
        include: {
            surveyQuestion: {
                include: {
                    questionOption: true,
                },
            },
            selectedOption: true,
            selectedOptions: true,
        },
        orderBy: {
            surveyQuestion: {
                order: "asc",
            },
        },
    });
}
// Get all responses (with optional pagination)
async function getAllResponses(skip, take) {
    return await prisma_1.default.response.findMany({
        skip,
        take,
        include: {
            surveyQuestion: {
                include: {
                    questionOption: true,
                },
            },
            selectedOption: true,
            selectedOptions: true,
            evaluation: {
                include: {
                    survey: true,
                    organization: true,
                },
            },
        },
        orderBy: {
            id: "desc",
        },
    });
}
