"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.bulkResponsesDTO = exports.bulkResponseItemDTO = exports.responseUpsertDTO = void 0;
const zod_1 = require("zod");
exports.responseUpsertDTO = zod_1.z.object({
    evaluationId: zod_1.z.string({ required_error: "Survey ID required" }).min(1),
    selectedOptionId: zod_1.z.string().optional(),
    selectedOptionIds: zod_1.z.array(zod_1.z.string()).optional(),
    freeResponseText: zod_1.z.string().optional(),
    responseId: zod_1.z.string().optional(),
});
exports.bulkResponseItemDTO = zod_1.z.object({
    surveyQuestionId: zod_1.z.string({ required_error: "Survey question ID required" }).min(1),
    selectedOptionId: zod_1.z.string().optional(),
    selectedOptionIds: zod_1.z.array(zod_1.z.string()).optional(),
    freeResponseText: zod_1.z.string().optional(),
    responseId: zod_1.z.string().optional(), // For updates
});
exports.bulkResponsesDTO = zod_1.z.object({
    evaluationId: zod_1.z.string({ required_error: "Evaluation ID required" }).min(1),
    responses: zod_1.z.array(exports.bulkResponseItemDTO).min(1, "At least one response required"),
});
