"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getAllResponses = getAllResponses;
const responses_db_1 = require("../responses.db");
async function getAllResponses(httpRequest) {
    try {
        // Parse pagination parameters from query string
        const { page, limit } = httpRequest.query || {};
        let skip;
        let take;
        if (page && limit) {
            const pageNum = parseInt(page, 10);
            const limitNum = parseInt(limit, 10);
            if (isNaN(pageNum) || isNaN(limitNum) || pageNum < 1 || limitNum < 1) {
                return {
                    statusCode: 400,
                    headers: { "Content-Type": "application/json" },
                    body: {
                        success: false,
                        error: "Invalid pagination parameters. Page and limit must be positive integers."
                    }
                };
            }
            skip = (pageNum - 1) * limitNum;
            take = limitNum;
        }
        else if (limit) {
            const limitNum = parseInt(limit, 10);
            if (isNaN(limitNum) || limitNum < 1) {
                return {
                    statusCode: 400,
                    headers: { "Content-Type": "application/json" },
                    body: {
                        success: false,
                        error: "Invalid limit parameter. Must be a positive integer."
                    }
                };
            }
            take = limitNum;
        }
        const responses = await responses_db_1.responsesDb.getAllResponses(skip, take);
        return {
            statusCode: 200,
            headers: { "Content-Type": "application/json" },
            body: {
                success: true,
                data: responses,
                count: responses.length,
                pagination: skip !== undefined && take !== undefined ? {
                    page: Math.floor(skip / take) + 1,
                    limit: take,
                    hasMore: responses.length === take
                } : undefined
            }
        };
    }
    catch (error) {
        return {
            statusCode: 500,
            headers: { "Content-Type": "application/json" },
            body: {
                success: false,
                error: "Internal server error",
                message: error.message
            }
        };
    }
}
