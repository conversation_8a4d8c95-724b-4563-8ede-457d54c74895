"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getResponse = getResponse;
const responses_db_1 = require("../responses.db");
async function getResponse(httpRequest) {
    try {
        const { id } = httpRequest.params;
        if (!id) {
            return {
                statusCode: 400,
                headers: { "Content-Type": "application/json" },
                body: {
                    success: false,
                    error: "Response ID is required"
                }
            };
        }
        const response = await responses_db_1.responsesDb.getResponse(id);
        if (!response) {
            return {
                statusCode: 404,
                headers: { "Content-Type": "application/json" },
                body: {
                    success: false,
                    error: "Response not found"
                }
            };
        }
        return {
            statusCode: 200,
            headers: { "Content-Type": "application/json" },
            body: {
                success: true,
                data: response
            }
        };
    }
    catch (error) {
        return {
            statusCode: 500,
            headers: { "Content-Type": "application/json" },
            body: {
                success: false,
                error: "Internal server error",
                message: error.message
            }
        };
    }
}
