"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.postResponse = postResponse;
const survey_questions_db_1 = require("../../survey-questions/survey-questions.db");
const responses_db_1 = require("../responses.db");
const responses_schemas_1 = require("../responses.schemas");
async function postResponse(httpRequest) {
    try {
        const { surveyQuestionId } = httpRequest.params;
        const feedbackInput = responses_schemas_1.responseUpsertDTO.parse(httpRequest.body);
        const surveyQuestion = await survey_questions_db_1.surveyQuestionsDb.getSurveyQuestionById(surveyQuestionId);
        if (!surveyQuestion) {
            throw new Error(`Survey question with id ${surveyQuestionId} not found`);
        }
        const validOptionIds = new Set(surveyQuestion.questionOption?.map(opt => opt.id));
        let args;
        switch (surveyQuestion.type) {
            case "MULTISELECT": {
                if (!feedbackInput.selectedOptionIds?.length) {
                    throw new Error('Survey question with type "MULTISELECT" must have at least one selected option');
                }
                const invalidIds = feedbackInput.selectedOptionIds.filter((id) => !validOptionIds.has(id));
                if (invalidIds.length) {
                    throw new Error(`Invalid selected option IDs for MULTISELECT: ${invalidIds.join(", ")}`);
                }
                args = {
                    create: {
                        surveyQuestion: { connect: { id: surveyQuestionId } },
                        evaluation: { connect: { id: feedbackInput.evaluationId } },
                        selectedOptions: {
                            connect: feedbackInput.selectedOptionIds.map((id) => ({ id })),
                        },
                    },
                    where: { id: feedbackInput.responseId },
                    update: {
                        selectedOptions: {
                            connect: feedbackInput.selectedOptionIds.map((id) => ({ id })),
                        },
                    },
                };
                break;
            }
            case "RADIO": {
                if (!feedbackInput.selectedOptionId) {
                    throw new Error('Survey question with type "RADIO" must have exactly one selected option');
                }
                if (!validOptionIds.has(feedbackInput.selectedOptionId)) {
                    throw new Error(`Invalid selected option ID for RADIO: ${feedbackInput.selectedOptionId}`);
                }
                args = {
                    create: {
                        surveyQuestion: { connect: { id: surveyQuestionId } },
                        evaluation: { connect: { id: feedbackInput.evaluationId } },
                        selectedOption: {
                            connect: { id: feedbackInput.selectedOptionId },
                        },
                    },
                    where: { id: feedbackInput.responseId },
                    update: {
                        selectedOption: {
                            connect: { id: feedbackInput.selectedOptionId },
                        },
                    },
                };
                break;
            }
            default:
                throw new Error(`Unsupported survey question type: ${surveyQuestion.type}`);
        }
        const data = await responses_db_1.responsesDb.postResponse(args);
        return {
            headers: { "Content-Type": "application/json" },
            statusCode: 200,
            body: { data },
        };
    }
    catch (error) {
        return {
            headers: { "Content-Type": "application/json" },
            statusCode: 400,
            body: { message: error instanceof Error ? error.message : String(error) },
        };
    }
}
