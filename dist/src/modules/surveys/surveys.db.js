"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.surveysDb = void 0;
const prisma_1 = __importDefault(require("../../prisma"));
exports.surveysDb = Object.freeze({
    getSurveyByName,
});
async function getSurveyByName(surveyName) {
    return await prisma_1.default.survey.findUnique({
        where: { name: surveyName },
        include: getSurveyDbSelect,
    });
}
const getSurveyDbSelect = {
    questions: {
        include: {
            shownIf: true,
            questionOption: true,
            controls: true,
            responses: { include: { selectedOption: true, selectedOptions: true } },
        },
    },
};
