"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.yearDataDb = void 0;
const prisma_1 = __importDefault(require("../../prisma"));
exports.yearDataDb = Object.freeze({
    createYearData,
    getYearData,
    getYearDataByOrg,
    updateYearData,
    deleteYearData,
    bulkCreateYearData,
});
// Year Data operations
async function createYearData(data) {
    return await prisma_1.default.yearData.create({
        data,
    });
}
async function getYearData(id) {
    return await prisma_1.default.yearData.findUnique({
        where: { id },
    });
}
async function getYearDataByOrg(orgId) {
    return await prisma_1.default.yearData.findMany({
        where: { orgId },
        orderBy: { year: "desc" },
    });
}
async function updateYearData(id, data) {
    return await prisma_1.default.yearData.update({
        where: { id },
        data,
    });
}
async function deleteYearData(id) {
    return await prisma_1.default.yearData.delete({
        where: { id },
    });
}
async function bulkCreateYearData(orgId, yearDataArray) {
    return await prisma_1.default.yearData.createMany({
        data: yearDataArray.map(item => ({
            orgId,
            year: item.year,
            MVIC: item.MVIC,
            EBITDA: item.EBITDA,
            OPM: item.OPM,
            netSales: item.netSales,
        })),
    });
}
