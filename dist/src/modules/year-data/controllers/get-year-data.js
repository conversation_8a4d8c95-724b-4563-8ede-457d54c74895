"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getYearData = getYearData;
const year_data_db_1 = require("../year-data.db");
async function getYearData(httpRequest) {
    try {
        const { id } = httpRequest.params;
        if (!id) {
            return {
                statusCode: 400,
                headers: { "Content-Type": "application/json" },
                body: {
                    success: false,
                    error: "Year data ID is required",
                },
            };
        }
        const yearData = await year_data_db_1.yearDataDb.getYearData(id);
        if (!yearData) {
            return {
                statusCode: 404,
                headers: { "Content-Type": "application/json" },
                body: {
                    success: false,
                    error: "Year data not found",
                },
            };
        }
        return {
            statusCode: 200,
            headers: { "Content-Type": "application/json" },
            body: {
                success: true,
                data: yearData,
            },
        };
    }
    catch (error) {
        return {
            statusCode: 500,
            headers: { "Content-Type": "application/json" },
            body: {
                success: false,
                error: "Internal server error",
                message: error.message,
            },
        };
    }
}
