"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateYearData = updateYearData;
const year_data_db_1 = require("../year-data.db");
const year_data_schemas_1 = require("../year-data.schemas");
async function updateYearData(httpRequest) {
    try {
        const { id } = httpRequest.params;
        const validatedData = year_data_schemas_1.yearDataUpdateDTO.parse(httpRequest.body);
        if (!id) {
            return {
                statusCode: 400,
                headers: { "Content-Type": "application/json" },
                body: {
                    success: false,
                    error: "Year data ID is required",
                },
            };
        }
        const yearData = await year_data_db_1.yearDataDb.updateYearData(id, validatedData);
        return {
            statusCode: 200,
            headers: { "Content-Type": "application/json" },
            body: {
                success: true,
                data: yearData,
                message: "Year data updated successfully",
            },
        };
    }
    catch (error) {
        if (error.name === "ZodError") {
            return {
                statusCode: 400,
                headers: { "Content-Type": "application/json" },
                body: {
                    success: false,
                    error: "Validation error",
                    details: error.errors,
                },
            };
        }
        if (error.code === "P2025") {
            return {
                statusCode: 404,
                headers: { "Content-Type": "application/json" },
                body: {
                    success: false,
                    error: "Year data not found",
                },
            };
        }
        return {
            statusCode: 500,
            headers: { "Content-Type": "application/json" },
            body: {
                success: false,
                error: "Internal server error",
                message: error.message,
            },
        };
    }
}
