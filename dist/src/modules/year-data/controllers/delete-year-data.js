"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteYearData = deleteYearData;
const year_data_db_1 = require("../year-data.db");
async function deleteYearData(httpRequest) {
    try {
        const { id } = httpRequest.params;
        if (!id) {
            return {
                statusCode: 400,
                headers: { "Content-Type": "application/json" },
                body: {
                    success: false,
                    error: "Year data ID is required",
                },
            };
        }
        await year_data_db_1.yearDataDb.deleteYearData(id);
        return {
            statusCode: 200,
            headers: { "Content-Type": "application/json" },
            body: {
                success: true,
                message: "Year data deleted successfully",
            },
        };
    }
    catch (error) {
        if (error.code === "P2025") {
            return {
                statusCode: 404,
                headers: { "Content-Type": "application/json" },
                body: {
                    success: false,
                    error: "Year data not found",
                },
            };
        }
        return {
            statusCode: 500,
            headers: { "Content-Type": "application/json" },
            body: {
                success: false,
                error: "Internal server error",
                message: error.message,
            },
        };
    }
}
