"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.surveyQuestionsDb = void 0;
const prisma_1 = __importDefault(require("../../prisma"));
exports.surveyQuestionsDb = Object.freeze({
    getSurveyQuestionById,
});
async function getSurveyQuestionById(surveyQuestionId) {
    return await prisma_1.default.surveyQuestion.findUnique({
        where: { id: surveyQuestionId },
        include: { questionOption: true },
    });
}
