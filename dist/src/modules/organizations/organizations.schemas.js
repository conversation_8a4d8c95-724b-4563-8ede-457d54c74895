"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.organizationResponseDTO = exports.organizationUpdateDTO = exports.organizationCreateDTO = void 0;
const zod_1 = require("zod");
exports.organizationCreateDTO = zod_1.z.object({
    name: zod_1.z.string({ required_error: "Organization name required" }).min(1),
    varId: zod_1.z.string({ required_error: "Var ID required" }).min(1),
    NAICSCode: zod_1.z.string().min(0).optional(),
    yearsInBusiness: zod_1.z.number().int().min(0).optional(),
    valuation: zod_1.z.number().optional(),
});
exports.organizationUpdateDTO = zod_1.z.object({
    name: zod_1.z.string().min(1).optional(),
    varId: zod_1.z.string().min(1).optional(),
    NAICSCode: zod_1.z.string().min(1).optional(),
    yearsInBusiness: zod_1.z.number().int().min(0).optional(),
    valuation: zod_1.z.number().optional(),
});
exports.organizationResponseDTO = zod_1.z.object({
    id: zod_1.z.string(),
    name: zod_1.z.string(),
    varId: zod_1.z.string(),
    NAICSCode: zod_1.z.string(),
    yearsInBusiness: zod_1.z.number(),
    valuation: zod_1.z.number().nullable(),
    yearData: zod_1.z
        .array(zod_1.z.object({
        id: zod_1.z.string(),
        year: zod_1.z.number(),
        MVIC: zod_1.z.number(),
        EBITDA: zod_1.z.number(),
        OPM: zod_1.z.number(),
        netSales: zod_1.z.number(),
    }))
        .optional(),
});
