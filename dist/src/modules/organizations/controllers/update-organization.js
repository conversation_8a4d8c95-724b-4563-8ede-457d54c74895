"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateOrganization = updateOrganization;
const organizations_db_1 = require("../organizations.db");
const organizations_schemas_1 = require("../organizations.schemas");
async function updateOrganization(httpRequest) {
    try {
        const { id } = httpRequest.params;
        const validatedData = organizations_schemas_1.organizationUpdateDTO.parse(httpRequest.body);
        if (!id) {
            return {
                statusCode: 400,
                headers: { "Content-Type": "application/json" },
                body: {
                    success: false,
                    error: "Organization ID is required",
                },
            };
        }
        const updateData = { ...validatedData };
        if (validatedData.varId) {
            updateData.var = { connect: { id: validatedData.varId } };
            delete updateData.varId;
        }
        const organization = await organizations_db_1.organizationsDb.updateOrganization(id, updateData);
        return {
            statusCode: 200,
            headers: { "Content-Type": "application/json" },
            body: {
                success: true,
                data: organization,
                message: "Organization updated successfully",
            },
        };
    }
    catch (error) {
        if (error.name === "ZodError") {
            return {
                statusCode: 400,
                headers: { "Content-Type": "application/json" },
                body: {
                    success: false,
                    error: "Validation error",
                    details: error.errors,
                },
            };
        }
        if (error.code === "P2025") {
            return {
                statusCode: 404,
                headers: { "Content-Type": "application/json" },
                body: {
                    success: false,
                    error: "Organization not found",
                },
            };
        }
        return {
            statusCode: 500,
            headers: { "Content-Type": "application/json" },
            body: {
                success: false,
                error: "Internal server error",
                message: error.message,
            },
        };
    }
}
