"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteOrganization = deleteOrganization;
const organizations_db_1 = require("../organizations.db");
async function deleteOrganization(httpRequest) {
    try {
        const { id } = httpRequest.params;
        if (!id) {
            return {
                statusCode: 400,
                headers: { "Content-Type": "application/json" },
                body: {
                    success: false,
                    error: "Organization ID is required",
                },
            };
        }
        await organizations_db_1.organizationsDb.deleteOrganization(id);
        return {
            statusCode: 200,
            headers: { "Content-Type": "application/json" },
            body: {
                success: true,
                message: "Organization deleted successfully",
            },
        };
    }
    catch (error) {
        if (error.code === "P2025") {
            return {
                statusCode: 404,
                headers: { "Content-Type": "application/json" },
                body: {
                    success: false,
                    error: "Organization not found",
                },
            };
        }
        return {
            statusCode: 500,
            headers: { "Content-Type": "application/json" },
            body: {
                success: false,
                error: "Internal server error",
                message: error.message,
            },
        };
    }
}
