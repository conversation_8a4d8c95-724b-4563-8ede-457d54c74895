"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.varsDb = void 0;
const prisma_1 = __importDefault(require("../../prisma"));
exports.varsDb = Object.freeze({
    createVar,
    getVar,
    getVars,
    updateVar,
    deleteVar,
});
// Var operations
async function createVar(data) {
    return await prisma_1.default.var.create({
        data,
        include: {
            organizations: {
                orderBy: { name: "asc" },
            },
        },
    });
}
async function getVar(id) {
    return await prisma_1.default.var.findUnique({
        where: { id },
        include: {
            organizations: {
                orderBy: { name: "asc" },
            },
        },
    });
}
async function getVars() {
    return await prisma_1.default.var.findMany({
        include: {
            organizations: {
                orderBy: { name: "asc" },
            },
        },
        orderBy: { name: "asc" },
    });
}
async function updateVar(id, data) {
    return await prisma_1.default.var.update({
        where: { id },
        data,
        include: {
            organizations: {
                orderBy: { name: "asc" },
            },
        },
    });
}
async function deleteVar(id) {
    return await prisma_1.default.var.delete({
        where: { id },
    });
}
