"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createVar = createVar;
const vars_db_1 = require("../vars.db");
const vars_schemas_1 = require("../vars.schemas");
async function createVar(httpRequest) {
    try {
        const validatedData = vars_schemas_1.varCreateDTO.parse(httpRequest.body);
        const varData = await vars_db_1.varsDb.createVar({
            name: validatedData.name,
        });
        return {
            statusCode: 201,
            headers: { "Content-Type": "application/json" },
            body: {
                success: true,
                data: varData,
                message: "Var created successfully",
            },
        };
    }
    catch (error) {
        if (error.name === "ZodError") {
            return {
                statusCode: 400,
                headers: { "Content-Type": "application/json" },
                body: {
                    success: false,
                    error: "Validation error",
                    details: error.errors,
                },
            };
        }
        if (error.code === "P2002") {
            return {
                statusCode: 409,
                headers: { "Content-Type": "application/json" },
                body: {
                    success: false,
                    error: "Var with this name already exists",
                },
            };
        }
        return {
            statusCode: 500,
            headers: { "Content-Type": "application/json" },
            body: {
                success: false,
                error: "Internal server error",
                message: error.message,
            },
        };
    }
}
