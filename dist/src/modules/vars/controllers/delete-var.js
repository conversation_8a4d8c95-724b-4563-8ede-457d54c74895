"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteVar = deleteVar;
const vars_db_1 = require("../vars.db");
async function deleteVar(httpRequest) {
    try {
        const { id } = httpRequest.params;
        if (!id) {
            return {
                statusCode: 400,
                headers: { "Content-Type": "application/json" },
                body: {
                    success: false,
                    error: "Var ID is required",
                },
            };
        }
        await vars_db_1.varsDb.deleteVar(id);
        return {
            statusCode: 200,
            headers: { "Content-Type": "application/json" },
            body: {
                success: true,
                message: "Var deleted successfully",
            },
        };
    }
    catch (error) {
        if (error.code === "P2025") {
            return {
                statusCode: 404,
                headers: { "Content-Type": "application/json" },
                body: {
                    success: false,
                    error: "Var not found",
                },
            };
        }
        if (error.code === "P2003") {
            return {
                statusCode: 409,
                headers: { "Content-Type": "application/json" },
                body: {
                    success: false,
                    error: "Cannot delete var because it has associated organizations",
                },
            };
        }
        return {
            statusCode: 500,
            headers: { "Content-Type": "application/json" },
            body: {
                success: false,
                error: "Internal server error",
                message: error.message,
            },
        };
    }
}
