"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getVar = getVar;
const vars_db_1 = require("../vars.db");
async function getVar(httpRequest) {
    try {
        const { id } = httpRequest.params;
        if (!id) {
            return {
                statusCode: 400,
                headers: { "Content-Type": "application/json" },
                body: {
                    success: false,
                    error: "Var ID is required",
                },
            };
        }
        const varData = await vars_db_1.varsDb.getVar(id);
        if (!varData) {
            return {
                statusCode: 404,
                headers: { "Content-Type": "application/json" },
                body: {
                    success: false,
                    error: "Var not found",
                },
            };
        }
        return {
            statusCode: 200,
            headers: { "Content-Type": "application/json" },
            body: {
                success: true,
                data: varData,
            },
        };
    }
    catch (error) {
        return {
            statusCode: 500,
            headers: { "Content-Type": "application/json" },
            body: {
                success: false,
                error: "Internal server error",
                message: error.message,
            },
        };
    }
}
