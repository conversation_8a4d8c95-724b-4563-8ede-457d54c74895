"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.completeEvaluation = completeEvaluation;
const evaluations_db_1 = require("../evaluations.db");
const scoring_1 = require("../../../utils/scoring");
async function completeEvaluation(httpRequest) {
    try {
        const { evaluationId } = httpRequest.params;
        if (!evaluationId) {
            return {
                statusCode: 400,
                headers: { "Content-Type": "application/json" },
                body: {
                    success: false,
                    error: "Evaluation ID is required",
                },
            };
        }
        // Get the evaluation with all responses and question options
        const evaluation = await evaluations_db_1.evaluationsDb.getEvaluationWithResponses(evaluationId);
        if (!evaluation) {
            return {
                statusCode: 404,
                headers: { "Content-Type": "application/json" },
                body: {
                    success: false,
                    error: "Evaluation not found",
                },
            };
        }
        // Check if evaluation is already completed
        if (evaluation.brandStrengthScore !== null) {
            return {
                statusCode: 409,
                headers: { "Content-Type": "application/json" },
                body: {
                    success: false,
                    error: "Evaluation is already completed",
                    data: {
                        evaluationId: evaluation.id,
                        brandStrengthScore: evaluation.brandStrengthScore,
                    },
                },
            };
        }
        // Prepare response data for new scoring system
        const responseData = evaluation.responses.map(response => ({
            surveyQuestionId: response.surveyQuestionId,
            selectedOptionId: response.selectedOption?.displayText,
            selectedOptionIds: response.selectedOptions?.map(opt => opt.displayText),
            freeResponseText: response.freeResponseText || undefined,
        }));
        // Prepare organization data for conditional scoring
        const organizationData = evaluation.organization
            ? {
                yearsInBusiness: evaluation.organization.yearsInBusiness,
            }
            : undefined;
        // Prepare database questions for scoring
        const databaseQuestions = evaluation.survey.questions.map(q => ({
            id: q.id,
            question: q.question,
            type: q.type,
            group: q.group,
            scoringRule: q.scoringRule || null, // Will be available after migration
            questionOption: q.questionOption,
        }));
        // Calculate scores using database-stored scoring rules
        const scoredResponses = (0, scoring_1.calculateResponseScoresFromDatabase)(responseData, databaseQuestions, organizationData);
        const brandStrengthScore = (0, scoring_1.calculateBrandStrengthScore)(scoredResponses);
        const scoreBreakdown = (0, scoring_1.getScoreBreakdown)(scoredResponses);
        // Update evaluation with calculated score
        const updatedEvaluation = await evaluations_db_1.evaluationsDb.updateEvaluationScore(evaluationId, brandStrengthScore);
        return {
            statusCode: 200,
            headers: { "Content-Type": "application/json" },
            body: {
                success: true,
                data: {
                    ...updatedEvaluation,
                    scoreBreakdown,
                    scoringDetails: scoredResponses,
                },
                message: "Evaluation completed successfully",
            },
        };
    }
    catch (error) {
        console.error("Error completing evaluation:", error);
        return {
            statusCode: 500,
            headers: { "Content-Type": "application/json" },
            body: {
                success: false,
                error: "Internal server error",
                message: error.message,
            },
        };
    }
}
