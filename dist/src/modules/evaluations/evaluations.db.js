"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.evaluationsDb = void 0;
const prisma_1 = __importDefault(require("../../prisma"));
exports.evaluationsDb = Object.freeze({
    postEvaluation,
    getEvaluationWithResponses,
    updateEvaluationScore,
    getEvaluation,
});
async function postEvaluation(surveyId) {
    return await prisma_1.default.evaluation.create({
        data: { survey: { connect: { id: surveyId } } },
    });
}
async function getEvaluation(evaluationId) {
    return await prisma_1.default.evaluation.findUnique({
        where: { id: evaluationId },
        include: {
            survey: true,
            organization: true,
        },
    });
}
async function getEvaluationWithResponses(evaluationId) {
    return await prisma_1.default.evaluation.findUnique({
        where: { id: evaluationId },
        include: {
            survey: {
                include: {
                    questions: {
                        include: {
                            questionOption: true,
                        },
                    },
                },
            },
            organization: true,
            responses: {
                include: {
                    surveyQuestion: {
                        include: {
                            questionOption: true,
                        },
                    },
                    selectedOption: true,
                    selectedOptions: true,
                },
            },
        },
    });
}
async function updateEvaluationScore(evaluationId, brandStrengthScore) {
    return await prisma_1.default.evaluation.update({
        where: { id: evaluationId },
        data: { brandStrengthScore },
        include: {
            survey: true,
            organization: true,
        },
    });
}
