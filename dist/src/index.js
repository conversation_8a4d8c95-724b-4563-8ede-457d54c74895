"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
require("dotenv/config");
const prisma_1 = __importDefault(require("./prisma"));
const server_1 = __importDefault(require("./server"));
const PORT = process.env.PORT || 4000;
async function main() {
    try {
        await server_1.default.listen(PORT, () => console.log(`\n🚀  Server listening on port: ${PORT} 🚀\n`));
    }
    catch (e) {
        console.error(e);
        await prisma_1.default.$disconnect();
    }
}
main();
