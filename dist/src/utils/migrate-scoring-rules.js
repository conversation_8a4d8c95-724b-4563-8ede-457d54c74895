"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.migrateScoringRulesToDatabase = migrateScoringRulesToDatabase;
exports.verifyScoringRulesMigration = verifyScoringRulesMigration;
const client_1 = require("@prisma/client");
const survey_1 = require("../../prisma/survey");
const prisma = new client_1.PrismaClient();
/**
 * Migrate scoring rules from survey.ts to database
 * This should be run after adding the scoringRule field to SurveyQuestion
 */
async function migrateScoringRulesToDatabase() {
    console.log("Starting migration of scoring rules to database...");
    try {
        // Get all survey questions with scoring rules from the survey.ts file
        const questionsWithRules = survey_1.SURVEY_QUESTIONS.filter(q => q.scoringRule);
        console.log(`Found ${questionsWithRules.length} questions with scoring rules`);
        for (const question of questionsWithRules) {
            console.log(`Migrating scoring rule for question ${question.id}...`);
            // Find the corresponding database question
            const dbQuestion = await prisma.surveyQuestion.findFirst({
                where: {
                    // We'll need to match by question text since IDs might be different
                    question: question.question,
                },
            });
            if (dbQuestion) {
                // Update the database question with the scoring rule
                await prisma.surveyQuestion.update({
                    where: { id: dbQuestion.id },
                    data: {
                        scoringRule: question.scoringRule,
                    },
                });
                console.log(`✅ Updated scoring rule for question: ${question.id}`);
            }
            else {
                console.log(`❌ Could not find database question for: ${question.id}`);
            }
        }
        console.log("✅ Migration completed successfully!");
    }
    catch (error) {
        console.error("❌ Migration failed:", error);
        throw error;
    }
    finally {
        await prisma.$disconnect();
    }
}
/**
 * Verify that scoring rules were migrated correctly
 */
async function verifyScoringRulesMigration() {
    console.log("Verifying scoring rules migration...");
    try {
        const questionsWithRules = await prisma.surveyQuestion.findMany({
            where: {
                scoringRule: {
                    not: { equals: null },
                },
            },
            select: {
                id: true,
                question: true,
                scoringRule: true,
            },
        });
        console.log(`Found ${questionsWithRules.length} questions with scoring rules in database:`);
        questionsWithRules.forEach(q => {
            console.log(`- ${q.id}: ${q.question.substring(0, 50)}...`);
            console.log(`  Rule type: ${q.scoringRule?.type}`);
        });
        return questionsWithRules;
    }
    catch (error) {
        console.error("❌ Verification failed:", error);
        throw error;
    }
    finally {
        await prisma.$disconnect();
    }
}
// Run migration if this file is executed directly
if (require.main === module) {
    migrateScoringRulesToDatabase()
        .then(() => verifyScoringRulesMigration())
        .catch(console.error);
}
