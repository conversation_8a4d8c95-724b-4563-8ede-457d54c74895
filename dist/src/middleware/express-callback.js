"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.makeExpressCallback = makeExpressCallback;
function makeExpressCallback(controller) {
    return function expressCallback(req, res) {
        const httpRequest = {
            body: req.body,
            query: req.query,
            params: req.params,
            ip: req.ip,
            method: req.method,
            path: req.path,
            headers: req.headers,
            cookies: req.headers.cookie,
        };
        if (req.path.includes("login")) {
            // eslint-disable-next-line @typescript-eslint/no-unused-vars
            const { password, ...rest } = req.body;
            console.log({
                path: `${req.baseUrl}${req.path}`,
                method: req.method,
                body: rest,
            });
        }
        else {
            console.log({
                path: `${req.baseUrl}${req.path}`,
                method: req.method,
                body: req.body,
            });
        }
        controller(httpRequest).then((httpResponse) => {
            if (httpResponse !== undefined) {
                if (httpResponse.headers) {
                    res.set(httpResponse.headers);
                }
                res.type("json");
                res.status(httpResponse.statusCode).send(httpResponse.body);
            }
        });
    };
}
