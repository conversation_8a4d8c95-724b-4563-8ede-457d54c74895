"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.applyMiddleware = applyMiddleware;
const express_1 = require("express");
const helmet_1 = __importDefault(require("helmet"));
function applyMiddleware(app) {
    app.use((0, helmet_1.default)());
    app.use((0, express_1.json)());
}
