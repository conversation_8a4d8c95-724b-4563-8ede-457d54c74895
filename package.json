{"name": "bw-server", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "nodemon --watch src --ext ts --exec ts-node src/index.ts", "build": "tsc", "start": "node dist/index.js", "prisma:generate": "prisma generate", "docker:dev": "docker-compose -f docker-compose.dev.yml up --build --remove-orphans", "valuate": "ts-node src/valuations.ts"}, "prisma": {"seed": "ts-node prisma/seed.ts"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@prisma/client": "^6.9.0", "dotenv": "^16.5.0", "express": "^5.1.0", "helmet": "^8.1.0", "prisma": "^6.9.0", "xlsx": "^0.18.5", "zod": "^3.25.57"}, "devDependencies": {"@types/express": "^5.0.2", "@types/node": "^22.15.29", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}