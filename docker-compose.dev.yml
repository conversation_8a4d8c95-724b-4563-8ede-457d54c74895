version: "3.4"
services:
    dev-api:
        container_name: bw-dev-api
        build:
            dockerfile: Dockerfile
            target: development
        volumes:
            - ./:/app
            - /app/node_modules
        ports:
            - 4040:4040
        command: yarn dev
        depends_on:
            - dev-db
        env_file:
            - .env.development

    dev-db:
        image: postgres:14
        restart: always
        container_name: bw-dev-db
        ports:
            - 5432:5432
        environment:
            - POSTGRES_USER=prisma
            - POSTGRES_PASSWORD=prisma
            - POSTGRES_DB=bw-dev-db
        volumes:
            - bw-dev-db:/var/lib/postgresql/data

volumes:
    bw-dev-db: {}
