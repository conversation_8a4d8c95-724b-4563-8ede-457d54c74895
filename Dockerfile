FROM node:20 as development

RUN npm i -g prisma@latest
RUN prisma version

WORKDIR /app
COPY package.json .
RUN yarn
COPY . .
RUN yarn prisma:generate
# To run the migration server, comment the line below
RUN yarn build

FROM node:16 as production

ARG NODE_ENV=production
ENV NODE_ENV=${NODE_ENV}

WORKDIR /app
COPY package.json .
RUN yarn --only=production

COPY --from=development /app/prisma ./prisma
RUN yarn prisma:generate

COPY --from=development /app/dist ./dist
CMD ["node", "dist/index.js"]