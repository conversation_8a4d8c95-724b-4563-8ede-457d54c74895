import { SURVEY_QUESTIONS } from "../../prisma/survey"

export interface ResponseData {
	surveyQuestionId: string
	selectedOptionId?: string
	selectedOptionIds?: string[]
	freeResponseText?: string
}

export interface OrganizationData {
	yearsInBusiness: number
	// Add other org fields as needed
}

export interface ScoredResponse {
	surveyQuestionId: string
	baseScore: number
	finalScore: number
	appliedRule?: string
}

/**
 * Calculate scores for survey responses, handling complex scoring rules
 */
export function calculateResponseScores(
	responses: ResponseData[],
	organizationData?: OrganizationData
): ScoredResponse[] {
	const scoredResponses: ScoredResponse[] = []
	const responseMap = new Map<string, ResponseData>()

	// Create a map for quick lookup
	responses.forEach(response => {
		responseMap.set(response.surveyQuestionId, response)
	})

	// First pass: Calculate base scores
	responses.forEach(response => {
		const question = SURVEY_QUESTIONS.find(q => q.id === response.surveyQuestionId)
		if (!question) return

		let baseScore = 0

		if (question.type === "FREE_RESPONSE") {
			// Free response questions don't have scores
			baseScore = 0
		} else if (question.type === "RADIO" && response.selectedOptionId) {
			// Find the selected option and get its score
			const selectedOption = question.options.find(
				opt =>
					// We need to match by display text since we don't have option IDs in the survey data
					// This is a simplified approach - in production you'd want proper option IDs
					opt.displayText === response.selectedOptionId
			)
			baseScore = selectedOption ? selectedOption.value : 0
		} else if (question.type === "MULTISELECT" && response.selectedOptionIds) {
			// Sum scores for all selected options
			baseScore = response.selectedOptionIds.reduce((sum, optionId) => {
				const selectedOption = question.options.find(opt => opt.displayText === optionId)
				return sum + (selectedOption ? selectedOption.scoreIfSelected : 0)
			}, 0)
		}

		scoredResponses.push({
			surveyQuestionId: response.surveyQuestionId,
			baseScore,
			finalScore: baseScore, // Will be updated in second pass if rules apply
		})
	})

	// Second pass: Apply scoring rules
	scoredResponses.forEach(scoredResponse => {
		const question = SURVEY_QUESTIONS.find(q => q.id === scoredResponse.surveyQuestionId)
		if (!question?.scoringRule) return

		const rule = question.scoringRule

		if (rule.type === "MULTIPLY") {
			// Find the target question's score
			const targetResponse = scoredResponses.find(
				sr => sr.surveyQuestionId === rule.targetQuestionId
			)
			if (targetResponse) {
				// Current question's score becomes the multiplier
				const multiplier = scoredResponse.baseScore
				targetResponse.finalScore = targetResponse.baseScore * multiplier
				targetResponse.appliedRule = `Multiplied by Q${question.id} (${multiplier}x)`

				// The multiplier question itself gets score 0 to avoid double counting
				scoredResponse.finalScore = 0
				scoredResponse.appliedRule = `Used as multiplier for Q${rule.targetQuestionId}`
			}
		} else if (rule.type === "CONDITIONAL" && rule.conditionalLogic && organizationData) {
			// Apply conditional scoring based on organization data
			const conditionalLogic = rule.conditionalLogic

			if (conditionalLogic.basedOn === "yearsInBusiness") {
				const yearsInBusiness = organizationData.yearsInBusiness
				const response = responseMap.get(scoredResponse.surveyQuestionId)

				if (response?.selectedOptionId) {
					// Find the matching condition
					let matchingCondition = null

					for (const condition of conditionalLogic.conditions) {
						if (evaluateCondition(yearsInBusiness, condition.condition)) {
							matchingCondition = condition
							break
						}
					}

					if (matchingCondition) {
						const conditionalScore =
							matchingCondition.optionScores[response.selectedOptionId]
						if (conditionalScore !== undefined) {
							scoredResponse.finalScore = conditionalScore
							scoredResponse.appliedRule = `Conditional scoring: ${yearsInBusiness} years (${matchingCondition.condition}) → ${conditionalScore} points`
						}
					}
				}
			}
		}
	})

	return scoredResponses
}

/**
 * Evaluate a condition string against a numeric value
 */
function evaluateCondition(value: number, condition: string): boolean {
	if (condition.startsWith("<")) {
		const threshold = parseInt(condition.substring(1))
		return value < threshold
	} else if (condition.startsWith(">")) {
		const threshold = parseInt(condition.substring(1))
		return value > threshold
	} else if (condition.includes("-")) {
		// Range condition like "4-6"
		const [min, max] = condition.split("-").map(s => parseInt(s.trim()))
		return value >= min && value <= max
	}
	return false
}

/**
 * Calculate total brand strength score from scored responses
 */
export function calculateBrandStrengthScore(scoredResponses: ScoredResponse[]): number {
	return scoredResponses.reduce((total, response) => total + response.finalScore, 0)
}

/**
 * Get scoring breakdown by group
 */
export function getScoreBreakdown(scoredResponses: ScoredResponse[]): Record<string, number> {
	const breakdown: Record<string, number> = {}

	scoredResponses.forEach(response => {
		const question = SURVEY_QUESTIONS.find(q => q.id === response.surveyQuestionId)
		if (question) {
			const group = question.group
			breakdown[group] = (breakdown[group] || 0) + response.finalScore
		}
	})

	return breakdown
}
