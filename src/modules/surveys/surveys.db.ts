import { Prisma } from "@prisma/client"
import prisma from "../../prisma"

export const surveysDb = Object.freeze({
	getSurveyByName,
})

async function getSurveyByName(surveyName: string) {
	return await prisma.survey.findUnique({
		where: { name: surveyName },
		include: getSurveyDbSelect,
	})
}

const getSurveyDbSelect = {
	questions: {
		include: {
			shownIf: true,
			questionOption: true,
			controls: true,
			responses: { include: { selectedOption: true, selectedOptions: true } },
		},
	},
} satisfies Prisma.SurveySelect
