import { HttpRequest, HttpResponse } from "../../../types/http"
import { surveysDb } from "../surveys.db"
export async function getSurvey(httpRequest: HttpRequest): Promise<HttpResponse | undefined> {
	try {
		const { surveyName } = httpRequest.params

		const data = await surveysDb.getSurveyByName(surveyName)

		return {
			headers: { "Content-Type": "application/json" },
			statusCode: 200,
			body: { data },
		}
	} catch (error) {
		return {
			headers: { "Content-Type": "application/json" },
			statusCode: 400,
			body: error,
		}
	}
}
