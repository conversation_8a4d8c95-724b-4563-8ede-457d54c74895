import { HttpRequest, HttpResponse } from "../../../types/http"
import { varsDb } from "../vars.db"

export async function getVar(httpRequest: HttpRequest): Promise<HttpResponse> {
	try {
		const { id } = httpRequest.params

		if (!id) {
			return {
				statusCode: 400,
				headers: { "Content-Type": "application/json" },
				body: {
					success: false,
					error: "Var ID is required",
				},
			}
		}

		const varData = await varsDb.getVar(id)

		if (!varData) {
			return {
				statusCode: 404,
				headers: { "Content-Type": "application/json" },
				body: {
					success: false,
					error: "Var not found",
				},
			}
		}

		return {
			statusCode: 200,
			headers: { "Content-Type": "application/json" },
			body: {
				success: true,
				data: varData,
			},
		}
	} catch (error: any) {
		return {
			statusCode: 500,
			headers: { "Content-Type": "application/json" },
			body: {
				success: false,
				error: "Internal server error",
				message: error.message,
			},
		}
	}
}
