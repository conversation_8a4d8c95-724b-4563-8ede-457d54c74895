import { HttpRequest, HttpResponse } from "../../../types/http"
import { varsDb } from "../vars.db"

export async function getVars(httpRequest: HttpRequest): Promise<HttpResponse> {
	try {
		const vars = await varsDb.getVars()

		return {
			statusCode: 200,
			body: {
				success: true,
				data: vars,
				count: vars.length
			}
		}
	} catch (error: any) {
		return {
			statusCode: 500,
			body: {
				success: false,
				error: "Internal server error",
				message: error.message
			}
		}
	}
}
