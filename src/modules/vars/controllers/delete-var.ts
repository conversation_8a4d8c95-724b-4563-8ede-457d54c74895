import { HttpRequest, HttpResponse } from "../../../types/http"
import { varsDb } from "../vars.db"

export async function deleteVar(httpRequest: HttpRequest): Promise<HttpResponse> {
	try {
		const { id } = httpRequest.params

		if (!id) {
			return {
				statusCode: 400,
				body: {
					success: false,
					error: "Var ID is required"
				}
			}
		}

		await varsDb.deleteVar(id)

		return {
			statusCode: 200,
			body: {
				success: true,
				message: "Var deleted successfully"
			}
		}
	} catch (error: any) {
		if (error.code === 'P2025') {
			return {
				statusCode: 404,
				body: {
					success: false,
					error: "Var not found"
				}
			}
		}

		if (error.code === 'P2003') {
			return {
				statusCode: 409,
				body: {
					success: false,
					error: "Cannot delete var because it has associated organizations"
				}
			}
		}

		return {
			statusCode: 500,
			body: {
				success: false,
				error: "Internal server error",
				message: error.message
			}
		}
	}
}
