import { HttpRequest, HttpResponse } from "../../../types/http"
import { varsDb } from "../vars.db"
import { varUpdateDTO, VarUpdateDTO } from "../vars.schemas"

export async function updateVar(httpRequest: HttpRequest): Promise<HttpResponse> {
	try {
		const { id } = httpRequest.params
		const validatedData: VarUpdateDTO = varUpdateDTO.parse(httpRequest.body)

		if (!id) {
			return {
				statusCode: 400,
				headers: { "Content-Type": "application/json" },
				body: {
					success: false,
					error: "Var ID is required",
				},
			}
		}

		const varData = await varsDb.updateVar(id, validatedData)

		return {
			statusCode: 200,
			headers: { "Content-Type": "application/json" },
			body: {
				success: true,
				data: varData,
				message: "Var updated successfully",
			},
		}
	} catch (error: any) {
		if (error.name === "ZodError") {
			return {
				statusCode: 400,
				headers: { "Content-Type": "application/json" },
				body: {
					success: false,
					error: "Validation error",
					details: error.errors,
				},
			}
		}

		if (error.code === "P2025") {
			return {
				statusCode: 404,
				headers: { "Content-Type": "application/json" },
				body: {
					success: false,
					error: "Var not found",
				},
			}
		}

		return {
			statusCode: 500,
			headers: { "Content-Type": "application/json" },
			body: {
				success: false,
				error: "Internal server error",
				message: error.message,
			},
		}
	}
}
