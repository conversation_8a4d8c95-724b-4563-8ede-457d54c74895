import { HttpRequest, HttpResponse } from "../../../types/http"
import { varsDb } from "../vars.db"
import { varCreateDTO, VarCreateDTO } from "../vars.schemas"

export async function createVar(httpRequest: HttpRequest): Promise<HttpResponse> {
	try {
		const validatedData: VarCreateDTO = varCreateDTO.parse(httpRequest.body)

		const varData = await varsDb.createVar({
			name: validatedData.name,
			description: validatedData.description,
		})

		return {
			statusCode: 201,
			headers: { "Content-Type": "application/json" },
			body: {
				success: true,
				data: varData,
				message: "Var created successfully",
			},
		}
	} catch (error: any) {
		if (error.name === "ZodError") {
			return {
				statusCode: 400,
				headers: { "Content-Type": "application/json" },
				body: {
					success: false,
					error: "Validation error",
					details: error.errors,
				},
			}
		}

		if (error.code === "P2002") {
			return {
				statusCode: 409,
				headers: { "Content-Type": "application/json" },
				body: {
					success: false,
					error: "Var with this name already exists",
				},
			}
		}

		return {
			statusCode: 500,
			headers: { "Content-Type": "application/json" },
			body: {
				success: false,
				error: "Internal server error",
				message: error.message,
			},
		}
	}
}
