import { z } from "zod"

// Var schemas
export type VarCreateDTO = z.infer<typeof varCreateDTO>
export const varCreateDTO = z.object({
	name: z.string({ required_error: "Var name required" }).min(1),
	description: z.string().optional(),
})

export type VarUpdateDTO = z.infer<typeof varUpdateDTO>
export const varUpdateDTO = z.object({
	name: z.string().min(1).optional(),
	description: z.string().optional(),
})

export type VarResponseDTO = z.infer<typeof varResponseDTO>
export const varResponseDTO = z.object({
	id: z.string(),
	name: z.string(),
	description: z.string().nullable(),
	organizations: z.array(z.object({
		id: z.string(),
		name: z.string(),
		NAICSCode: z.string(),
		yearsInBusiness: z.number(),
		valuation: z.number().nullable(),
	})).optional(),
})
