import { Router } from "express"

import { makeExpressCallback } from "../../middleware/express-callback"
import { createVar } from "./controllers/create-var"
import { getVar } from "./controllers/get-var"
import { getVars } from "./controllers/get-vars"
import { updateVar } from "./controllers/update-var"
import { deleteVar } from "./controllers/delete-var"

export const router = Router()

// Var routes
router.post("/", makeExpressCallback(createVar))
router.get("/", makeExpressCallback(getVars))
router.get("/:id", makeExpressCallback(getVar))
router.put("/:id", makeExpressCallback(updateVar))
router.delete("/:id", makeExpressCallback(deleteVar))
