import { Prisma } from "@prisma/client"
import prisma from "../../prisma"

export const varsDb = Object.freeze({
	createVar,
	getVar,
	getVars,
	updateVar,
	deleteVar,
})

// Var operations
async function createVar(data: Prisma.VarCreateInput) {
	return await prisma.var.create({
		data,
		include: {
			organizations: {
				orderBy: { name: "asc" },
			},
		},
	})
}

async function getVar(id: string) {
	return await prisma.var.findUnique({
		where: { id },
		include: {
			organizations: {
				orderBy: { name: "asc" },
			},
		},
	})
}

async function getVars() {
	return await prisma.var.findMany({
		include: {
			organizations: {
				orderBy: { name: "asc" },
			},
		},
		orderBy: { name: "asc" },
	})
}

async function updateVar(id: string, data: Prisma.VarUpdateInput) {
	return await prisma.var.update({
		where: { id },
		data,
		include: {
			organizations: {
				orderBy: { name: "asc" },
			},
		},
	})
}

async function deleteVar(id: string) {
	return await prisma.var.delete({
		where: { id },
	})
}
