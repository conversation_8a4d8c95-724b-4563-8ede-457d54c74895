import { Router } from "express"

import { makeExpressCallback } from "../../middleware/express-callback"
import { postResponse } from "./controllers/post-response"
import { postBulkResponses } from "./controllers/post-bulk-responses"
import { getResponse } from "./controllers/get-response"
import { getResponsesByEvaluation } from "./controllers/get-responses-by-evaluation"
import { getAllResponses } from "./controllers/get-all-responses"

export const router = Router()

// GET routes
router.get("/", makeExpressCallback(getAllResponses))
router.get("/evaluation/:evaluationId", makeExpressCallback(getResponsesByEvaluation))
router.get("/:id", makeExpressCallback(getResponse))

// POST routes
router.get("/:surveyName", makeExpressCallback(postResponse)) // Legacy route
router.post("/bulk", makeExpressCallback(postBulkResponses))
