import { HttpRequest, HttpResponse } from "../../../types/http"
import { responsesDb } from "../responses.db"

export async function getResponsesByEvaluation(httpRequest: HttpRequest): Promise<HttpResponse> {
	try {
		const { evaluationId } = httpRequest.params

		if (!evaluationId) {
			return {
				statusCode: 400,
				headers: { "Content-Type": "application/json" },
				body: {
					success: false,
					error: "Evaluation ID is required"
				}
			}
		}

		const responses = await responsesDb.getResponsesByEvaluation(evaluationId)

		return {
			statusCode: 200,
			headers: { "Content-Type": "application/json" },
			body: {
				success: true,
				data: responses,
				count: responses.length
			}
		}
	} catch (error: any) {
		return {
			statusCode: 500,
			headers: { "Content-Type": "application/json" },
			body: {
				success: false,
				error: "Internal server error",
				message: error.message
			}
		}
	}
}
