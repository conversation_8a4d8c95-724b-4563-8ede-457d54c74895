import { HttpRequest, HttpResponse } from "../../../types/http"
import { surveyQuestionsDb } from "../../survey-questions/survey-questions.db"
import { responsesDb } from "../responses.db"
import { responseUpsertDTO } from "../responses.schemas"

export async function postResponse(httpRequest: HttpRequest): Promise<HttpResponse | undefined> {
	try {
		const { surveyQuestionId } = httpRequest.params
		const feedbackInput = responseUpsertDTO.parse(httpRequest.body)

		const surveyQuestion = await surveyQuestionsDb.getSurveyQuestionById(surveyQuestionId)

		if (!surveyQuestion) {
			throw new Error(`Survey question with id ${surveyQuestionId} not found`)
		}

		const validOptionIds = new Set(surveyQuestion.questionOption?.map(opt => opt.id))

		let args: Parameters<typeof responsesDb.postResponse>[0]

		switch (surveyQuestion.type) {
			case "MULTISELECT": {
				if (!feedbackInput.selectedOptionIds?.length) {
					throw new Error(
						'Survey question with type "MULTISELECT" must have at least one selected option'
					)
				}
				const invalidIds = feedbackInput.selectedOptionIds.filter(
					(id: string) => !validOptionIds.has(id)
				)

				if (invalidIds.length) {
					throw new Error(
						`Invalid selected option IDs for MULTISELECT: ${invalidIds.join(", ")}`
					)
				}

				args = {
					create: {
						surveyQuestion: { connect: { id: surveyQuestionId } },
						evaluation: { connect: { id: feedbackInput.evaluationId } },
						selectedOptions: {
							connect: feedbackInput.selectedOptionIds.map((id: string) => ({ id })),
						},
					},
					where: { id: feedbackInput.responseId },
					update: {
						selectedOptions: {
							connect: feedbackInput.selectedOptionIds.map((id: string) => ({ id })),
						},
					},
				}
				break
			}
			case "RADIO": {
				if (!feedbackInput.selectedOptionId) {
					throw new Error(
						'Survey question with type "RADIO" must have exactly one selected option'
					)
				}

				if (!validOptionIds.has(feedbackInput.selectedOptionId)) {
					throw new Error(
						`Invalid selected option ID for RADIO: ${feedbackInput.selectedOptionId}`
					)
				}
				args = {
					create: {
						surveyQuestion: { connect: { id: surveyQuestionId } },
						evaluation: { connect: { id: feedbackInput.evaluationId } },
						selectedOption: {
							connect: { id: feedbackInput.selectedOptionId },
						},
					},
					where: { id: feedbackInput.responseId },
					update: {
						selectedOption: {
							connect: { id: feedbackInput.selectedOptionId },
						},
					},
				}
				break
			}
			default:
				throw new Error(`Unsupported survey question type: ${surveyQuestion.type}`)
		}

		const data = await responsesDb.postResponse(args)
		return {
			headers: { "Content-Type": "application/json" },
			statusCode: 200,
			body: { data },
		}
	} catch (error) {
		return {
			headers: { "Content-Type": "application/json" },
			statusCode: 400,
			body: { message: error instanceof Error ? error.message : String(error) },
		}
	}
}
