import { HttpRequest, HttpResponse } from "../../../types/http"
import { responsesDb } from "../responses.db"

export async function getResponse(httpRequest: HttpRequest): Promise<HttpResponse> {
	try {
		const { id } = httpRequest.params

		if (!id) {
			return {
				statusCode: 400,
				headers: { "Content-Type": "application/json" },
				body: {
					success: false,
					error: "Response ID is required"
				}
			}
		}

		const response = await responsesDb.getResponse(id)

		if (!response) {
			return {
				statusCode: 404,
				headers: { "Content-Type": "application/json" },
				body: {
					success: false,
					error: "Response not found"
				}
			}
		}

		return {
			statusCode: 200,
			headers: { "Content-Type": "application/json" },
			body: {
				success: true,
				data: response
			}
		}
	} catch (error: any) {
		return {
			statusCode: 500,
			headers: { "Content-Type": "application/json" },
			body: {
				success: false,
				error: "Internal server error",
				message: error.message
			}
		}
	}
}
