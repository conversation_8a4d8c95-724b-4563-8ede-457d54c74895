import { Prisma } from "@prisma/client"
import prisma from "../../prisma"

export const responsesDb = Object.freeze({
	postResponse: postResponseUpsert,
	createResponse,
	updateResponse,
	getResponse,
	getResponsesByEvaluation,
	getAllResponses,
})

// Legacy function for backward compatibility
async function postResponseUpsert(args: Prisma.ResponseUpsertArgs) {
	return await prisma.response.upsert({ ...args })
}

// Create a new response
async function createResponse(
	evaluationId: string,
	surveyQuestionId: string,
	selectedOptionId?: string,
	selectedOptionIds?: string[]
) {
	const data: Prisma.ResponseCreateInput = {
		evaluation: { connect: { id: evaluationId } },
		surveyQuestion: { connect: { id: surveyQuestionId } },
	}

	// Handle single option selection
	if (selectedOptionId) {
		data.selectedOption = { connect: { id: selectedOptionId } }
	}

	// Handle multiple option selection
	if (selectedOptionIds && selectedOptionIds.length > 0) {
		data.selectedOptions = {
			connect: selectedOptionIds.map(id => ({ id })),
		}
	}

	return await prisma.response.create({ data })
}

// Update an existing response
async function updateResponse(
	responseId: string,
	selectedOptionId?: string,
	selectedOptionIds?: string[]
) {
	const data: Prisma.ResponseUpdateInput = {}

	// Handle single option selection
	if (selectedOptionId) {
		data.selectedOption = { connect: { id: selectedOptionId } }
		// Disconnect any existing multiple selections
		data.selectedOptions = { set: [] }
	}

	// Handle multiple option selection
	if (selectedOptionIds && selectedOptionIds.length > 0) {
		data.selectedOptions = {
			set: selectedOptionIds.map(id => ({ id })),
		}
		// Disconnect any existing single selection
		data.selectedOption = { disconnect: true }
	}

	// If neither is provided, clear all selections
	if (!selectedOptionId && (!selectedOptionIds || selectedOptionIds.length === 0)) {
		data.selectedOption = { disconnect: true }
		data.selectedOptions = { set: [] }
	}

	return await prisma.response.update({
		where: { id: responseId },
		data,
	})
}

// Get a single response by ID
async function getResponse(responseId: string) {
	return await prisma.response.findUnique({
		where: { id: responseId },
		include: {
			surveyQuestion: {
				include: {
					questionOption: true,
				},
			},
			selectedOption: true,
			selectedOptions: true,
			evaluation: {
				include: {
					survey: true,
					organization: true,
				},
			},
		},
	})
}

// Get all responses for a specific evaluation
async function getResponsesByEvaluation(evaluationId: string) {
	return await prisma.response.findMany({
		where: { evaluationId },
		include: {
			surveyQuestion: {
				include: {
					questionOption: true,
				},
			},
			selectedOption: true,
			selectedOptions: true,
		},
		orderBy: {
			surveyQuestion: {
				order: "asc",
			},
		},
	})
}

// Get all responses (with optional pagination)
async function getAllResponses(skip?: number, take?: number) {
	return await prisma.response.findMany({
		skip,
		take,
		include: {
			surveyQuestion: {
				include: {
					questionOption: true,
				},
			},
			selectedOption: true,
			selectedOptions: true,
			evaluation: {
				include: {
					survey: true,
					organization: true,
				},
			},
		},
		orderBy: {
			id: "desc",
		},
	})
}
