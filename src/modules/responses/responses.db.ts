import { Prisma } from "@prisma/client"
import prisma from "../../prisma"

export const responsesDb = Object.freeze({
	postResponse: postResponseUpsert,
	createResponse,
	updateResponse,
})

// Legacy function for backward compatibility
async function postResponseUpsert(args: Prisma.ResponseUpsertArgs) {
	return await prisma.response.upsert({ ...args })
}

// Create a new response
async function createResponse(
	evaluationId: string,
	surveyQuestionId: string,
	selectedOptionId?: string,
	selectedOptionIds?: string[]
) {
	const data: Prisma.ResponseCreateInput = {
		evaluation: { connect: { id: evaluationId } },
		surveyQuestion: { connect: { id: surveyQuestionId } },
	}

	// Handle single option selection
	if (selectedOptionId) {
		data.selectedOption = { connect: { id: selectedOptionId } }
	}

	// Handle multiple option selection
	if (selectedOptionIds && selectedOptionIds.length > 0) {
		data.selectedOptions = {
			connect: selectedOptionIds.map(id => ({ id })),
		}
	}

	return await prisma.response.create({ data })
}

// Update an existing response
async function updateResponse(
	responseId: string,
	selectedOptionId?: string,
	selectedOptionIds?: string[]
) {
	const data: Prisma.ResponseUpdateInput = {}

	// Handle single option selection
	if (selectedOptionId) {
		data.selectedOption = { connect: { id: selectedOptionId } }
		// Disconnect any existing multiple selections
		data.selectedOptions = { set: [] }
	}

	// Handle multiple option selection
	if (selectedOptionIds && selectedOptionIds.length > 0) {
		data.selectedOptions = {
			set: selectedOptionIds.map(id => ({ id })),
		}
		// Disconnect any existing single selection
		data.selectedOption = { disconnect: true }
	}

	// If neither is provided, clear all selections
	if (!selectedOptionId && (!selectedOptionIds || selectedOptionIds.length === 0)) {
		data.selectedOption = { disconnect: true }
		data.selectedOptions = { set: [] }
	}

	return await prisma.response.update({
		where: { id: responseId },
		data,
	})
}
