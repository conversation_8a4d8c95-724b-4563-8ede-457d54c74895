import { z } from "zod"

export type ResponseUpsertDTO = z.infer<typeof responseUpsertDTO>
export const responseUpsertDTO = z.object({
	evaluationId: z.string({ required_error: "Survey ID required" }).min(1),
	selectedOptionId: z.string().optional(),
	selectedOptionIds: z.array(z.string()).optional(),
	freeResponseText: z.string().optional(),
	responseId: z.string().optional(),
})

// Single response for bulk operations
export type BulkResponseItemDTO = z.infer<typeof bulkResponseItemDTO>
export const bulkResponseItemDTO = z.object({
	surveyQuestionId: z.string({ required_error: "Survey question ID required" }).min(1),
	selectedOptionId: z.string().optional(),
	selectedOptionIds: z.array(z.string()).optional(),
	freeResponseText: z.string().optional(),
	responseId: z.string().optional(), // For updates
})

// Bulk responses schema
export type BulkResponsesDTO = z.infer<typeof bulkResponsesDTO>
export const bulkResponsesDTO = z.object({
	evaluationId: z.string({ required_error: "Evaluation ID required" }).min(1),
	responses: z.array(bulkResponseItemDTO).min(1, "At least one response required"),
})
