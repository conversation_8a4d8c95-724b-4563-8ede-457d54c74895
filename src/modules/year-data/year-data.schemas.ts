import { z } from "zod"

// Year Data schemas
export type YearDataCreateDTO = z.infer<typeof yearDataCreateDTO>
export const yearDataCreateDTO = z.object({
	orgId: z.string({ required_error: "Organization ID required" }).min(1),
	year: z.number({ required_error: "Year required" }).int().min(1900).max(2100),
	MVIC: z.number({ required_error: "MVIC required" }),
	EBITDA: z.number({ required_error: "EBITDA required" }),
	OPM: z.number({ required_error: "OPM required" }),
	netSales: z.number({ required_error: "Net Sales required" }),
})

export type YearDataUpdateDTO = z.infer<typeof yearDataUpdateDTO>
export const yearDataUpdateDTO = z.object({
	year: z.number().int().min(1900).max(2100).optional(),
	MVIC: z.number().optional(),
	EBITDA: z.number().optional(),
	OPM: z.number().optional(),
	netSales: z.number().optional(),
})

export type YearDataResponseDTO = z.infer<typeof yearDataResponseDTO>
export const yearDataResponseDTO = z.object({
	id: z.string(),
	orgId: z.string(),
	year: z.number(),
	MVIC: z.number(),
	EBITDA: z.number(),
	OPM: z.number(),
	netSales: z.number(),
})

// Bulk year data creation for multiple years at once
export type BulkYearDataCreateDTO = z.infer<typeof bulkYearDataCreateDTO>
export const bulkYearDataCreateDTO = z.object({
	orgId: z.string({ required_error: "Organization ID required" }).min(1),
	yearData: z.array(z.object({
		year: z.number().int().min(1900).max(2100),
		MVIC: z.number(),
		EBITDA: z.number(),
		OPM: z.number(),
		netSales: z.number(),
	})).min(1, "At least one year of data required"),
})
