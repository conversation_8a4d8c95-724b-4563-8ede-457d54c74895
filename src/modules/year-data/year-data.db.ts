import { Prisma } from "@prisma/client"
import prisma from "../../prisma"

export const yearDataDb = Object.freeze({
	createYearData,
	getYearData,
	getYearDataByOrg,
	updateYearData,
	deleteYearData,
	bulkCreateYearData,
})

// Year Data operations
async function createYearData(data: Prisma.YearDataCreateInput) {
	return await prisma.yearData.create({
		data,
	})
}

async function getYearData(id: string) {
	return await prisma.yearData.findUnique({
		where: { id },
	})
}

async function getYearDataByOrg(orgId: string) {
	return await prisma.yearData.findMany({
		where: { orgId },
		orderBy: { year: "desc" },
	})
}

async function updateYearData(id: string, data: Prisma.YearDataUpdateInput) {
	return await prisma.yearData.update({
		where: { id },
		data,
	})
}

async function deleteYearData(id: string) {
	return await prisma.yearData.delete({
		where: { id },
	})
}

async function bulkCreateYearData(
	orgId: string,
	yearDataArray: Omit<Prisma.YearDataCreateInput, "org">[]
) {
	return await prisma.yearData.createMany({
		data: yearDataArray.map(item => ({
			orgId,
			year: item.year as number,
			MVIC: item.MVIC as number,
			EBITDA: item.EBITDA as number,
			OPM: item.OPM as number,
			netSales: item.netSales as number,
		})),
	})
}
