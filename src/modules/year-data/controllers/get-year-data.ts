import { HttpRequest, HttpResponse } from "../../../types/http"
import { yearDataDb } from "../year-data.db"

export async function getYearData(httpRequest: HttpRequest): Promise<HttpResponse> {
	try {
		const { id } = httpRequest.params

		if (!id) {
			return {
				statusCode: 400,
				body: {
					success: false,
					error: "Year data ID is required"
				}
			}
		}

		const yearData = await yearDataDb.getYearData(id)

		if (!yearData) {
			return {
				statusCode: 404,
				body: {
					success: false,
					error: "Year data not found"
				}
			}
		}

		return {
			statusCode: 200,
			body: {
				success: true,
				data: yearData
			}
		}
	} catch (error: any) {
		return {
			statusCode: 500,
			body: {
				success: false,
				error: "Internal server error",
				message: error.message
			}
		}
	}
}
