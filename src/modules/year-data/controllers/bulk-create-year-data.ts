import { HttpRequest, HttpResponse } from "../../../types/http"
import { yearDataDb } from "../year-data.db"
import { 
	bulkYearDataCreateDTO,
	BulkYearDataCreateDTO 
} from "../year-data.schemas"

export async function bulkCreateYearData(httpRequest: HttpRequest): Promise<HttpResponse> {
	try {
		const validatedData: BulkYearDataCreateDTO = bulkYearDataCreateDTO.parse(httpRequest.body)
		
		const result = await yearDataDb.bulkCreateYearData(
			validatedData.orgId,
			validatedData.yearData
		)

		return {
			statusCode: 201,
			body: {
				success: true,
				data: result,
				message: `${result.count} year data records created successfully`
			}
		}
	} catch (error: any) {
		if (error.name === 'ZodError') {
			return {
				statusCode: 400,
				body: {
					success: false,
					error: "Validation error",
					details: error.errors
				}
			}
		}

		if (error.code === 'P2002') {
			return {
				statusCode: 409,
				body: {
					success: false,
					error: "Some year data already exists for this organization"
				}
			}
		}

		return {
			statusCode: 500,
			body: {
				success: false,
				error: "Internal server error",
				message: error.message
			}
		}
	}
}
