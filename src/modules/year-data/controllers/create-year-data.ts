import { HttpRequest, HttpResponse } from "../../../types/http"
import { yearDataDb } from "../year-data.db"
import { yearDataCreateDTO, YearDataCreateDTO } from "../year-data.schemas"

export async function createYearData(httpRequest: HttpRequest): Promise<HttpResponse> {
	try {
		const validatedData: YearDataCreateDTO = yearDataCreateDTO.parse(httpRequest.body)

		const yearData = await yearDataDb.createYearData({
			year: validatedData.year,
			MVIC: validatedData.MVIC,
			EBITDA: validatedData.EBITDA,
			OPM: validatedData.OPM,
			netSales: validatedData.netSales,
			org: {
				connect: { id: validatedData.orgId },
			},
		})

		return {
			statusCode: 201,
			headers: { "Content-Type": "application/json" },
			body: {
				success: true,
				data: yearData,
				message: "Year data created successfully",
			},
		}
	} catch (error: any) {
		if (error.name === "ZodError") {
			return {
				statusCode: 400,
				headers: { "Content-Type": "application/json" },
				body: {
					success: false,
					error: "Validation error",
					details: error.errors,
				},
			}
		}

		if (error.code === "P2002") {
			return {
				statusCode: 409,
				headers: { "Content-Type": "application/json" },
				body: {
					success: false,
					error: "Year data for this organization and year already exists",
				},
			}
		}

		return {
			statusCode: 500,
			headers: { "Content-Type": "application/json" },
			body: {
				success: false,
				error: "Internal server error",
				message: error.message,
			},
		}
	}
}
