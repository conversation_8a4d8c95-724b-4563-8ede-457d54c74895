import { HttpRequest, HttpResponse } from "../../../types/http"
import { yearDataDb } from "../year-data.db"

export async function deleteYearData(httpRequest: HttpRequest): Promise<HttpResponse> {
	try {
		const { id } = httpRequest.params

		if (!id) {
			return {
				statusCode: 400,
				headers: { "Content-Type": "application/json" },
				body: {
					success: false,
					error: "Year data ID is required",
				},
			}
		}

		await yearDataDb.deleteYearData(id)

		return {
			statusCode: 200,
			headers: { "Content-Type": "application/json" },
			body: {
				success: true,
				message: "Year data deleted successfully",
			},
		}
	} catch (error: any) {
		if (error.code === "P2025") {
			return {
				statusCode: 404,
				headers: { "Content-Type": "application/json" },
				body: {
					success: false,
					error: "Year data not found",
				},
			}
		}

		return {
			statusCode: 500,
			headers: { "Content-Type": "application/json" },
			body: {
				success: false,
				error: "Internal server error",
				message: error.message,
			},
		}
	}
}
