import { HttpRequest, HttpResponse } from "../../../types/http"
import { yearDataDb } from "../year-data.db"

export async function getYearDataByOrg(httpRequest: HttpRequest): Promise<HttpResponse> {
	try {
		const { orgId } = httpRequest.params

		if (!orgId) {
			return {
				statusCode: 400,
				headers: { "Content-Type": "application/json" },
				body: {
					success: false,
					error: "Organization ID is required",
				},
			}
		}

		const yearData = await yearDataDb.getYearDataByOrg(orgId)

		return {
			statusCode: 200,
			headers: { "Content-Type": "application/json" },
			body: {
				success: true,
				data: yearData,
				count: yearData.length,
			},
		}
	} catch (error: any) {
		return {
			statusCode: 500,
			headers: { "Content-Type": "application/json" },
			body: {
				success: false,
				error: "Internal server error",
				message: error.message,
			},
		}
	}
}
