import { HttpRequest, HttpResponse } from "../../../types/http"
import { yearDataDb } from "../year-data.db"
import { yearDataUpdateDTO, YearDataUpdateDTO } from "../year-data.schemas"

export async function updateYearData(httpRequest: HttpRequest): Promise<HttpResponse> {
	try {
		const { id } = httpRequest.params
		const validatedData: YearDataUpdateDTO = yearDataUpdateDTO.parse(httpRequest.body)

		if (!id) {
			return {
				statusCode: 400,
				headers: { "Content-Type": "application/json" },
				body: {
					success: false,
					error: "Year data ID is required",
				},
			}
		}

		const yearData = await yearDataDb.updateYearData(id, validatedData)

		return {
			statusCode: 200,
			headers: { "Content-Type": "application/json" },
			body: {
				success: true,
				data: yearData,
				message: "Year data updated successfully",
			},
		}
	} catch (error: any) {
		if (error.name === "ZodError") {
			return {
				statusCode: 400,
				headers: { "Content-Type": "application/json" },
				body: {
					success: false,
					error: "Validation error",
					details: error.errors,
				},
			}
		}

		if (error.code === "P2025") {
			return {
				statusCode: 404,
				headers: { "Content-Type": "application/json" },
				body: {
					success: false,
					error: "Year data not found",
				},
			}
		}

		return {
			statusCode: 500,
			headers: { "Content-Type": "application/json" },
			body: {
				success: false,
				error: "Internal server error",
				message: error.message,
			},
		}
	}
}
