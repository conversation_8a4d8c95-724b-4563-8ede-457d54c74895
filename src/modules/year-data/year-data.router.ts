import { Router } from "express"

import { makeExpressCallback } from "../../middleware/express-callback"
import { createYearData } from "./controllers/create-year-data"
import { bulkCreateYearData } from "./controllers/bulk-create-year-data"
import { getYearData } from "./controllers/get-year-data"
import { getYearDataByOrg } from "./controllers/get-year-data-by-org"
import { updateYearData } from "./controllers/update-year-data"
import { deleteYearData } from "./controllers/delete-year-data"

export const router = Router()

// Year data routes
router.post("/", makeExpressCallback(createYearData))
router.post("/bulk", makeExpressCallback(bulkCreateYearData))
router.get("/:id", makeExpressCallback(getYearData))
router.get("/organization/:orgId", makeExpressCallback(getYearDataByOrg))
router.put("/:id", makeExpressCallback(updateYearData))
router.delete("/:id", makeExpressCallback(deleteYearData))
