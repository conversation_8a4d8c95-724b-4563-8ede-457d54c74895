import { HttpRequest, HttpResponse } from "../../../types/http"
import { evaluationsDb } from "../evaluations.db"
import { calculateBrandStrengthScore } from "../services/brand-strength-calculator"

export async function completeEvaluation(httpRequest: HttpRequest): Promise<HttpResponse> {
	try {
		const { evaluationId } = httpRequest.params

		if (!evaluationId) {
			return {
				statusCode: 400,
				headers: { "Content-Type": "application/json" },
				body: {
					success: false,
					error: "Evaluation ID is required",
				},
			}
		}

		// Get the evaluation with all responses and question options
		const evaluation = await evaluationsDb.getEvaluationWithResponses(evaluationId)

		if (!evaluation) {
			return {
				statusCode: 404,
				headers: { "Content-Type": "application/json" },
				body: {
					success: false,
					error: "Evaluation not found",
				},
			}
		}

		// Check if evaluation is already completed
		if (evaluation.brandStrengthScore !== null) {
			return {
				statusCode: 409,
				headers: { "Content-Type": "application/json" },
				body: {
					success: false,
					error: "Evaluation is already completed",
					data: {
						evaluationId: evaluation.id,
						brandStrengthScore: evaluation.brandStrengthScore,
					},
				},
			}
		}

		// Calculate brand strength score
		const brandStrengthScore = calculateBrandStrengthScore(evaluation.responses)

		// Update evaluation with calculated score
		const updatedEvaluation = await evaluationsDb.updateEvaluationScore(
			evaluationId,
			brandStrengthScore
		)

		return {
			statusCode: 200,
			headers: { "Content-Type": "application/json" },
			body: {
				success: true,
				data: updatedEvaluation,
				message: "Evaluation completed successfully",
			},
		}
	} catch (error: any) {
		console.error("Error completing evaluation:", error)

		return {
			statusCode: 500,
			headers: { "Content-Type": "application/json" },
			body: {
				success: false,
				error: "Internal server error",
				message: error.message,
			},
		}
	}
}
