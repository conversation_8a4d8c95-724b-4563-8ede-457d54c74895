import { HttpRequest, HttpResponse } from "../../../types/http"
import { evaluationsDb } from "../evaluations.db"
import {
	calculateResponseScores,
	calculateBrandStrengthScore as calculateNewBrandStrengthScore,
	getScoreBreakdown,
} from "../../../utils/scoring"

export async function completeEvaluation(httpRequest: HttpRequest): Promise<HttpResponse> {
	try {
		const { evaluationId } = httpRequest.params

		if (!evaluationId) {
			return {
				statusCode: 400,
				headers: { "Content-Type": "application/json" },
				body: {
					success: false,
					error: "Evaluation ID is required",
				},
			}
		}

		// Get the evaluation with all responses and question options
		const evaluation = await evaluationsDb.getEvaluationWithResponses(evaluationId)

		if (!evaluation) {
			return {
				statusCode: 404,
				headers: { "Content-Type": "application/json" },
				body: {
					success: false,
					error: "Evaluation not found",
				},
			}
		}

		// Check if evaluation is already completed
		if (evaluation.brandStrengthScore !== null) {
			return {
				statusCode: 409,
				headers: { "Content-Type": "application/json" },
				body: {
					success: false,
					error: "Evaluation is already completed",
					data: {
						evaluationId: evaluation.id,
						brandStrengthScore: evaluation.brandStrengthScore,
					},
				},
			}
		}

		// Prepare response data for new scoring system
		const responseData = evaluation.responses.map(response => ({
			surveyQuestionId: response.surveyQuestionId,
			selectedOptionId: response.selectedOption?.displayText,
			selectedOptionIds: response.selectedOptions?.map(opt => opt.displayText),
			freeResponseText: response.freeResponseText || undefined,
		}))

		// Prepare organization data for conditional scoring
		const organizationData = evaluation.organization
			? {
					yearsInBusiness: evaluation.organization.yearsInBusiness,
			  }
			: undefined

		// Calculate scores using new scoring system with rules
		const scoredResponses = calculateResponseScores(responseData, organizationData)
		const brandStrengthScore = calculateNewBrandStrengthScore(scoredResponses)
		const scoreBreakdown = getScoreBreakdown(scoredResponses)

		// Update evaluation with calculated score
		const updatedEvaluation = await evaluationsDb.updateEvaluationScore(
			evaluationId,
			brandStrengthScore
		)

		return {
			statusCode: 200,
			headers: { "Content-Type": "application/json" },
			body: {
				success: true,
				data: {
					...updatedEvaluation,
					scoreBreakdown,
					scoringDetails: scoredResponses,
				},
				message: "Evaluation completed successfully",
			},
		}
	} catch (error: any) {
		console.error("Error completing evaluation:", error)

		return {
			statusCode: 500,
			headers: { "Content-Type": "application/json" },
			body: {
				success: false,
				error: "Internal server error",
				message: error.message,
			},
		}
	}
}
