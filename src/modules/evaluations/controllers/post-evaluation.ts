import { HttpRequest, HttpResponse } from "../../../types/http"
import { evaluationsDb } from "../evaluations.db"
export async function postEvaluation(httpRequest: HttpRequest): Promise<HttpResponse | undefined> {
	try {
		const { surveyId } = httpRequest.params

		const data = await evaluationsDb.postEvaluation(surveyId)

		return {
			headers: { "Content-Type": "application/json" },
			statusCode: 200,
			body: { data },
		}
	} catch (error) {
		return {
			headers: { "Content-Type": "application/json" },
			statusCode: 400,
			body: error,
		}
	}
}
