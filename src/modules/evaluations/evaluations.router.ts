import { Router } from "express"

import { makeExpressCallback } from "../../middleware/express-callback"
import { postEvaluation } from "./controllers/post-evaluation"
import { completeEvaluation } from "./controllers/complete-evaluation"

export const router = Router()

router.post("/:surveyId", makeExpressCallback(postEvaluation))
router.put("/:evaluationId/complete", makeExpressCallback(completeEvaluation))
