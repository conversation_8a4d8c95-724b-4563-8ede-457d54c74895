import prisma from "../../prisma"

export const evaluationsDb = Object.freeze({
	postEvaluation,
	getEvaluationWithResponses,
	updateEvaluationScore,
	getEvaluation,
})

async function postEvaluation(surveyId: string) {
	return await prisma.evaluation.create({
		data: { survey: { connect: { id: surveyId } } },
	})
}

async function getEvaluation(evaluationId: string) {
	return await prisma.evaluation.findUnique({
		where: { id: evaluationId },
		include: {
			survey: true,
			organization: true,
		},
	})
}

async function getEvaluationWithResponses(evaluationId: string) {
	return await prisma.evaluation.findUnique({
		where: { id: evaluationId },
		include: {
			survey: {
				include: {
					questions: {
						include: {
							questionOption: true,
						},
					},
				},
			},
			organization: true,
			responses: {
				include: {
					surveyQuestion: {
						include: {
							questionOption: true,
						},
					},
					selectedOption: true,
					selectedOptions: true,
				},
			},
		},
	})
}

async function updateEvaluationScore(evaluationId: string, brandStrengthScore: number) {
	return await prisma.evaluation.update({
		where: { id: evaluationId },
		data: { brandStrengthScore },
		include: {
			survey: true,
			organization: true,
		},
	})
}
