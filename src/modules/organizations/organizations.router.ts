import { Router } from "express"

import { makeExpressCallback } from "../../middleware/express-callback"
import {
	createOrganization,
	getOrganization,
	getOrganizations,
	updateOrganization,
	deleteOrganization,
} from "./controllers/organizations.controller"
import {
	createYearData,
	bulkCreateYearData,
	getYearData,
	getYearDataByOrg,
	updateYearData,
	deleteYearData,
} from "./controllers/year-data.controller"

export const router = Router()

// Organization routes
router.post("/", makeExpressCallback(createOrganization))
router.get("/", makeExpressCallback(getOrganizations))
router.get("/:id", makeExpressCallback(getOrganization))
router.put("/:id", makeExpressCallback(updateOrganization))
router.delete("/:id", makeExpressCallback(deleteOrganization))

// Year data routes
router.post("/year-data", makeExpressCallback(createYearData))
router.post("/year-data/bulk", makeExpressCallback(bulkCreateYearData))
router.get("/year-data/:id", makeExpressCallback(getYearData))
router.get("/:orgId/year-data", makeExpressCallback(getYearDataByOrg))
router.put("/year-data/:id", makeExpressCallback(updateYearData))
router.delete("/year-data/:id", makeExpressCallback(deleteYearData))
