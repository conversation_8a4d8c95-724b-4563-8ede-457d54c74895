import { Router } from "express"

import { makeExpressCallback } from "../../middleware/express-callback"
import { createOrganization } from "./controllers/create-organization"
import { getOrganization } from "./controllers/get-organization"
import { getOrganizations } from "./controllers/get-organizations"
import { updateOrganization } from "./controllers/update-organization"
import { deleteOrganization } from "./controllers/delete-organization"

export const router = Router()

// Organization routes
router.post("/", makeExpressCallback(createOrganization))
router.get("/", makeExpressCallback(getOrganizations))
router.get("/:id", makeExpressCallback(getOrganization))
router.put("/:id", makeExpressCallback(updateOrganization))
router.delete("/:id", makeExpressCallback(deleteOrganization))
