import { HttpRequest, HttpResponse } from "../../../types/http"
import { organizationsDb } from "../organizations.db"

export async function getOrganization(httpRequest: HttpRequest): Promise<HttpResponse> {
	try {
		const { id } = httpRequest.params

		if (!id) {
			return {
				statusCode: 400,
				headers: { "Content-Type": "application/json" },
				body: {
					success: false,
					error: "Organization ID is required",
				},
			}
		}

		const organization = await organizationsDb.getOrganization(id)

		if (!organization) {
			return {
				statusCode: 404,
				headers: { "Content-Type": "application/json" },
				body: {
					success: false,
					error: "Organization not found",
				},
			}
		}

		return {
			statusCode: 200,
			headers: { "Content-Type": "application/json" },
			body: {
				success: true,
				data: organization,
			},
		}
	} catch (error: any) {
		return {
			statusCode: 500,
			headers: { "Content-Type": "application/json" },
			body: {
				success: false,
				error: "Internal server error",
				message: error.message,
			},
		}
	}
}
