import { HttpRequest, HttpResponse } from "../../../types/http"
import { organizationsDb } from "../organizations.db"
import { 
	organizationCreateDTO, 
	organizationUpdateDTO,
	OrganizationCreateDTO,
	OrganizationUpdateDTO 
} from "../organizations.schemas"

export async function createOrganization(httpRequest: HttpRequest): Promise<HttpResponse> {
	try {
		const validatedData: OrganizationCreateDTO = organizationCreateDTO.parse(httpRequest.body)
		
		const organization = await organizationsDb.createOrganization({
			name: validatedData.name,
			NAICSCode: validatedData.NAICSCode,
			yearsInBusiness: validatedData.yearsInBusiness,
			valuation: validatedData.valuation,
			var: {
				connect: { id: validatedData.varId }
			}
		})

		return {
			statusCode: 201,
			body: {
				success: true,
				data: organization,
				message: "Organization created successfully"
			}
		}
	} catch (error: any) {
		if (error.name === 'ZodError') {
			return {
				statusCode: 400,
				body: {
					success: false,
					error: "Validation error",
					details: error.errors
				}
			}
		}

		if (error.code === 'P2002') {
			return {
				statusCode: 409,
				body: {
					success: false,
					error: "Organization with this name already exists"
				}
			}
		}

		return {
			statusCode: 500,
			body: {
				success: false,
				error: "Internal server error",
				message: error.message
			}
		}
	}
}

export async function getOrganization(httpRequest: HttpRequest): Promise<HttpResponse> {
	try {
		const { id } = httpRequest.params

		if (!id) {
			return {
				statusCode: 400,
				body: {
					success: false,
					error: "Organization ID is required"
				}
			}
		}

		const organization = await organizationsDb.getOrganization(id)

		if (!organization) {
			return {
				statusCode: 404,
				body: {
					success: false,
					error: "Organization not found"
				}
			}
		}

		return {
			statusCode: 200,
			body: {
				success: true,
				data: organization
			}
		}
	} catch (error: any) {
		return {
			statusCode: 500,
			body: {
				success: false,
				error: "Internal server error",
				message: error.message
			}
		}
	}
}

export async function getOrganizations(httpRequest: HttpRequest): Promise<HttpResponse> {
	try {
		const organizations = await organizationsDb.getOrganizations()

		return {
			statusCode: 200,
			body: {
				success: true,
				data: organizations,
				count: organizations.length
			}
		}
	} catch (error: any) {
		return {
			statusCode: 500,
			body: {
				success: false,
				error: "Internal server error",
				message: error.message
			}
		}
	}
}

export async function updateOrganization(httpRequest: HttpRequest): Promise<HttpResponse> {
	try {
		const { id } = httpRequest.params
		const validatedData: OrganizationUpdateDTO = organizationUpdateDTO.parse(httpRequest.body)

		if (!id) {
			return {
				statusCode: 400,
				body: {
					success: false,
					error: "Organization ID is required"
				}
			}
		}

		const updateData: any = { ...validatedData }
		if (validatedData.varId) {
			updateData.var = { connect: { id: validatedData.varId } }
			delete updateData.varId
		}

		const organization = await organizationsDb.updateOrganization(id, updateData)

		return {
			statusCode: 200,
			body: {
				success: true,
				data: organization,
				message: "Organization updated successfully"
			}
		}
	} catch (error: any) {
		if (error.name === 'ZodError') {
			return {
				statusCode: 400,
				body: {
					success: false,
					error: "Validation error",
					details: error.errors
				}
			}
		}

		if (error.code === 'P2025') {
			return {
				statusCode: 404,
				body: {
					success: false,
					error: "Organization not found"
				}
			}
		}

		return {
			statusCode: 500,
			body: {
				success: false,
				error: "Internal server error",
				message: error.message
			}
		}
	}
}

export async function deleteOrganization(httpRequest: HttpRequest): Promise<HttpResponse> {
	try {
		const { id } = httpRequest.params

		if (!id) {
			return {
				statusCode: 400,
				body: {
					success: false,
					error: "Organization ID is required"
				}
			}
		}

		await organizationsDb.deleteOrganization(id)

		return {
			statusCode: 200,
			body: {
				success: true,
				message: "Organization deleted successfully"
			}
		}
	} catch (error: any) {
		if (error.code === 'P2025') {
			return {
				statusCode: 404,
				body: {
					success: false,
					error: "Organization not found"
				}
			}
		}

		return {
			statusCode: 500,
			body: {
				success: false,
				error: "Internal server error",
				message: error.message
			}
		}
	}
}
