import { HttpRequest, HttpResponse } from "../../../types/http"
import { organizationsDb } from "../organizations.db"
import { 
	yearDataCreateDTO, 
	yearDataUpdateDTO,
	bulkYearDataCreateDTO,
	YearDataCreateDTO,
	YearDataUpdateDTO,
	BulkYearDataCreateDTO 
} from "../organizations.schemas"

export async function createYearData(httpRequest: HttpRequest): Promise<HttpResponse> {
	try {
		const validatedData: YearDataCreateDTO = yearDataCreateDTO.parse(httpRequest.body)
		
		const yearData = await organizationsDb.createYearData({
			year: validatedData.year,
			MVIC: validatedData.MVIC,
			EBITDA: validatedData.EBITDA,
			OPM: validatedData.OPM,
			netSales: validatedData.netSales,
			org: {
				connect: { id: validatedData.orgId }
			}
		})

		return {
			statusCode: 201,
			body: {
				success: true,
				data: yearData,
				message: "Year data created successfully"
			}
		}
	} catch (error: any) {
		if (error.name === 'ZodError') {
			return {
				statusCode: 400,
				body: {
					success: false,
					error: "Validation error",
					details: error.errors
				}
			}
		}

		if (error.code === 'P2002') {
			return {
				statusCode: 409,
				body: {
					success: false,
					error: "Year data for this organization and year already exists"
				}
			}
		}

		return {
			statusCode: 500,
			body: {
				success: false,
				error: "Internal server error",
				message: error.message
			}
		}
	}
}

export async function bulkCreateYearData(httpRequest: HttpRequest): Promise<HttpResponse> {
	try {
		const validatedData: BulkYearDataCreateDTO = bulkYearDataCreateDTO.parse(httpRequest.body)
		
		const result = await organizationsDb.bulkCreateYearData(
			validatedData.orgId,
			validatedData.yearData
		)

		return {
			statusCode: 201,
			body: {
				success: true,
				data: result,
				message: `${result.count} year data records created successfully`
			}
		}
	} catch (error: any) {
		if (error.name === 'ZodError') {
			return {
				statusCode: 400,
				body: {
					success: false,
					error: "Validation error",
					details: error.errors
				}
			}
		}

		if (error.code === 'P2002') {
			return {
				statusCode: 409,
				body: {
					success: false,
					error: "Some year data already exists for this organization"
				}
			}
		}

		return {
			statusCode: 500,
			body: {
				success: false,
				error: "Internal server error",
				message: error.message
			}
		}
	}
}

export async function getYearData(httpRequest: HttpRequest): Promise<HttpResponse> {
	try {
		const { id } = httpRequest.params

		if (!id) {
			return {
				statusCode: 400,
				body: {
					success: false,
					error: "Year data ID is required"
				}
			}
		}

		const yearData = await organizationsDb.getYearData(id)

		if (!yearData) {
			return {
				statusCode: 404,
				body: {
					success: false,
					error: "Year data not found"
				}
			}
		}

		return {
			statusCode: 200,
			body: {
				success: true,
				data: yearData
			}
		}
	} catch (error: any) {
		return {
			statusCode: 500,
			body: {
				success: false,
				error: "Internal server error",
				message: error.message
			}
		}
	}
}

export async function getYearDataByOrg(httpRequest: HttpRequest): Promise<HttpResponse> {
	try {
		const { orgId } = httpRequest.params

		if (!orgId) {
			return {
				statusCode: 400,
				body: {
					success: false,
					error: "Organization ID is required"
				}
			}
		}

		const yearData = await organizationsDb.getYearDataByOrg(orgId)

		return {
			statusCode: 200,
			body: {
				success: true,
				data: yearData,
				count: yearData.length
			}
		}
	} catch (error: any) {
		return {
			statusCode: 500,
			body: {
				success: false,
				error: "Internal server error",
				message: error.message
			}
		}
	}
}

export async function updateYearData(httpRequest: HttpRequest): Promise<HttpResponse> {
	try {
		const { id } = httpRequest.params
		const validatedData: YearDataUpdateDTO = yearDataUpdateDTO.parse(httpRequest.body)

		if (!id) {
			return {
				statusCode: 400,
				body: {
					success: false,
					error: "Year data ID is required"
				}
			}
		}

		const yearData = await organizationsDb.updateYearData(id, validatedData)

		return {
			statusCode: 200,
			body: {
				success: true,
				data: yearData,
				message: "Year data updated successfully"
			}
		}
	} catch (error: any) {
		if (error.name === 'ZodError') {
			return {
				statusCode: 400,
				body: {
					success: false,
					error: "Validation error",
					details: error.errors
				}
			}
		}

		if (error.code === 'P2025') {
			return {
				statusCode: 404,
				body: {
					success: false,
					error: "Year data not found"
				}
			}
		}

		return {
			statusCode: 500,
			body: {
				success: false,
				error: "Internal server error",
				message: error.message
			}
		}
	}
}

export async function deleteYearData(httpRequest: HttpRequest): Promise<HttpResponse> {
	try {
		const { id } = httpRequest.params

		if (!id) {
			return {
				statusCode: 400,
				body: {
					success: false,
					error: "Year data ID is required"
				}
			}
		}

		await organizationsDb.deleteYearData(id)

		return {
			statusCode: 200,
			body: {
				success: true,
				message: "Year data deleted successfully"
			}
		}
	} catch (error: any) {
		if (error.code === 'P2025') {
			return {
				statusCode: 404,
				body: {
					success: false,
					error: "Year data not found"
				}
			}
		}

		return {
			statusCode: 500,
			body: {
				success: false,
				error: "Internal server error",
				message: error.message
			}
		}
	}
}
