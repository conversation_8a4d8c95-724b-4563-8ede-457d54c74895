import { HttpRequest, HttpResponse } from "../../../types/http"
import { organizationsDb } from "../organizations.db"

export async function deleteOrganization(httpRequest: HttpRequest): Promise<HttpResponse> {
	try {
		const { id } = httpRequest.params

		if (!id) {
			return {
				statusCode: 400,
				body: {
					success: false,
					error: "Organization ID is required"
				}
			}
		}

		await organizationsDb.deleteOrganization(id)

		return {
			statusCode: 200,
			body: {
				success: true,
				message: "Organization deleted successfully"
			}
		}
	} catch (error: any) {
		if (error.code === 'P2025') {
			return {
				statusCode: 404,
				body: {
					success: false,
					error: "Organization not found"
				}
			}
		}

		return {
			statusCode: 500,
			body: {
				success: false,
				error: "Internal server error",
				message: error.message
			}
		}
	}
}
