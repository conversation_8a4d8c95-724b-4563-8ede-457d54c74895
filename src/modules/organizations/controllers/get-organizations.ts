import { HttpRequest, HttpResponse } from "../../../types/http"
import { organizationsDb } from "../organizations.db"

export async function getOrganizations(httpRequest: HttpRequest): Promise<HttpResponse> {
	try {
		const organizations = await organizationsDb.getOrganizations()

		return {
			statusCode: 200,
			headers: { "Content-Type": "application/json" },
			body: {
				success: true,
				data: organizations,
				count: organizations.length,
			},
		}
	} catch (error: any) {
		return {
			statusCode: 500,
			headers: { "Content-Type": "application/json" },
			body: {
				success: false,
				error: "Internal server error",
				message: error.message,
			},
		}
	}
}
