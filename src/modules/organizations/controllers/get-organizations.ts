import { HttpRequest, HttpResponse } from "../../../types/http"
import { organizationsDb } from "../organizations.db"

export async function getOrganizations(httpRequest: HttpRequest): Promise<HttpResponse> {
	try {
		const organizations = await organizationsDb.getOrganizations()

		return {
			statusCode: 200,
			body: {
				success: true,
				data: organizations,
				count: organizations.length
			}
		}
	} catch (error: any) {
		return {
			statusCode: 500,
			body: {
				success: false,
				error: "Internal server error",
				message: error.message
			}
		}
	}
}
