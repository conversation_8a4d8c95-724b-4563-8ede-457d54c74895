import { HttpRequest, HttpResponse } from "../../../types/http"
import { organizationsDb } from "../organizations.db"
import { organizationCreateDTO, OrganizationCreateDTO } from "../organizations.schemas"

export async function createOrganization(httpRequest: HttpRequest): Promise<HttpResponse> {
	try {
		const validatedData: OrganizationCreateDTO = organizationCreateDTO.parse(httpRequest.body)

		const organization = await organizationsDb.createOrganization({
			name: validatedData.name,
			NAICSCode: validatedData.NAICSCode || "",
			yearsInBusiness: validatedData.yearsInBusiness || 0,
			valuation: validatedData.valuation,
			var: {
				connect: { id: validatedData.varId },
			},
		})

		return {
			statusCode: 201,
			headers: { "Content-Type": "application/json" },
			body: {
				success: true,
				data: organization,
				message: "Organization created successfully",
			},
		}
	} catch (error: any) {
		if (error.name === "ZodError") {
			return {
				statusCode: 400,
				headers: { "Content-Type": "application/json" },
				body: {
					success: false,
					error: "Validation error",
					details: error.errors,
				},
			}
		}

		if (error.code === "P2002") {
			return {
				statusCode: 409,
				headers: { "Content-Type": "application/json" },
				body: {
					success: false,
					error: "Organization with this name already exists",
				},
			}
		}

		return {
			statusCode: 500,
			headers: { "Content-Type": "application/json" },
			body: {
				success: false,
				error: "Internal server error",
				message: error.message,
			},
		}
	}
}
