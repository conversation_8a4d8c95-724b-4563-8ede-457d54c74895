import { HttpRequest, HttpResponse } from "../../../types/http"
import { organizationsDb } from "../organizations.db"
import { organizationUpdateDTO, OrganizationUpdateDTO } from "../organizations.schemas"

export async function updateOrganization(httpRequest: HttpRequest): Promise<HttpResponse> {
	try {
		const { id } = httpRequest.params
		const validatedData: OrganizationUpdateDTO = organizationUpdateDTO.parse(httpRequest.body)

		if (!id) {
			return {
				statusCode: 400,
				headers: { "Content-Type": "application/json" },
				body: {
					success: false,
					error: "Organization ID is required",
				},
			}
		}

		const updateData: any = { ...validatedData }
		if (validatedData.varId) {
			updateData.var = { connect: { id: validatedData.varId } }
			delete updateData.varId
		}

		const organization = await organizationsDb.updateOrganization(id, updateData)

		return {
			statusCode: 200,
			headers: { "Content-Type": "application/json" },
			body: {
				success: true,
				data: organization,
				message: "Organization updated successfully",
			},
		}
	} catch (error: any) {
		if (error.name === "ZodError") {
			return {
				statusCode: 400,
				headers: { "Content-Type": "application/json" },
				body: {
					success: false,
					error: "Validation error",
					details: error.errors,
				},
			}
		}

		if (error.code === "P2025") {
			return {
				statusCode: 404,
				headers: { "Content-Type": "application/json" },
				body: {
					success: false,
					error: "Organization not found",
				},
			}
		}

		return {
			statusCode: 500,
			headers: { "Content-Type": "application/json" },
			body: {
				success: false,
				error: "Internal server error",
				message: error.message,
			},
		}
	}
}
