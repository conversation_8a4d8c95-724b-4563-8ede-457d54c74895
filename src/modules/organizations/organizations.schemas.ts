import { z } from "zod"

// Organization schemas
export type OrganizationCreateDTO = z.infer<typeof organizationCreateDTO>
export const organizationCreateDTO = z.object({
	name: z.string({ required_error: "Organization name required" }).min(1),
	varId: z.string({ required_error: "Var ID required" }).min(1),
	NAICSCode: z.string().min(0).optional(),
	yearsInBusiness: z.number().int().min(0).optional(),
	valuation: z.number().optional(),
})

export type OrganizationUpdateDTO = z.infer<typeof organizationUpdateDTO>
export const organizationUpdateDTO = z.object({
	name: z.string().min(1).optional(),
	varId: z.string().min(1).optional(),
	NAICSCode: z.string().min(1).optional(),
	yearsInBusiness: z.number().int().min(0).optional(),
	valuation: z.number().optional(),
})

export type OrganizationResponseDTO = z.infer<typeof organizationResponseDTO>
export const organizationResponseDTO = z.object({
	id: z.string(),
	name: z.string(),
	varId: z.string(),
	NAICSCode: z.string(),
	yearsInBusiness: z.number(),
	valuation: z.number().nullable(),
	yearData: z
		.array(
			z.object({
				id: z.string(),
				year: z.number(),
				MVIC: z.number(),
				EBITDA: z.number(),
				OPM: z.number(),
				netSales: z.number(),
			})
		)
		.optional(),
})
