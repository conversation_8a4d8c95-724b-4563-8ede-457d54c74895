import { Prisma } from "@prisma/client"
import prisma from "../../prisma"

export const organizationsDb = Object.freeze({
	// Organization CRUD
	createOrganization,
	getOrganization,
	getOrganizations,
	updateOrganization,
	deleteOrganization,

	// Year Data CRUD
	createYearData,
	getYearData,
	getYearDataByOrg,
	updateYearD<PERSON>,
	deleteYearData,
	bulkCreateYearData,
})

// Organization operations
async function createOrganization(data: Prisma.OrganizationCreateInput) {
	return await prisma.organization.create({
		data,
		include: {
			yearData: {
				orderBy: { year: "desc" },
			},
		},
	})
}

async function getOrganization(id: string) {
	return await prisma.organization.findUnique({
		where: { id },
		include: {
			yearData: {
				orderBy: { year: "desc" },
			},
		},
	})
}

async function getOrganizations() {
	return await prisma.organization.findMany({
		include: {
			yearData: {
				orderBy: { year: "desc" },
			},
		},
		orderBy: { name: "asc" },
	})
}

async function updateOrganization(id: string, data: Prisma.OrganizationUpdateInput) {
	return await prisma.organization.update({
		where: { id },
		data,
		include: {
			yearData: {
				orderBy: { year: "desc" },
			},
		},
	})
}

async function deleteOrganization(id: string) {
	return await prisma.organization.delete({
		where: { id },
	})
}

// Year Data operations
async function createYearData(data: Prisma.YearDataCreateInput) {
	return await prisma.yearData.create({
		data,
	})
}

async function getYearData(id: string) {
	return await prisma.yearData.findUnique({
		where: { id },
	})
}

async function getYearDataByOrg(orgId: string) {
	return await prisma.yearData.findMany({
		where: { orgId },
		orderBy: { year: "desc" },
	})
}

async function updateYearData(id: string, data: Prisma.YearDataUpdateInput) {
	return await prisma.yearData.update({
		where: { id },
		data,
	})
}

async function deleteYearData(id: string) {
	return await prisma.yearData.delete({
		where: { id },
	})
}

async function bulkCreateYearData(
	orgId: string,
	yearDataArray: Omit<Prisma.YearDataCreateInput, "org">[]
) {
	const data = yearDataArray.map(yearData => ({
		...yearData,
		org: { connect: { id: orgId } },
	}))

	return await prisma.yearData.createMany({
		data: data.map(item => ({
			orgId,
			year: item.year as number,
			MVIC: item.MVIC as number,
			EBITDA: item.EBITDA as number,
			OPM: item.OPM as number,
			netSales: item.netSales as number,
		})),
	})
}
