import { Prisma } from "@prisma/client"
import prisma from "../../prisma"

export const organizationsDb = Object.freeze({
	// Organization CRUD
	createOrganization,
	getOrganization,
	getOrganizations,
	updateOrganization,
	deleteOrganization,
})

// Organization operations
async function createOrganization(data: Prisma.OrganizationCreateInput) {
	return await prisma.organization.create({
		data,
		include: {
			yearData: {
				orderBy: { year: "desc" },
			},
		},
	})
}

async function getOrganization(id: string) {
	return await prisma.organization.findUnique({
		where: { id },
		include: {
			yearData: {
				orderBy: { year: "desc" },
			},
		},
	})
}

async function getOrganizations() {
	return await prisma.organization.findMany({
		include: {
			yearData: {
				orderBy: { year: "desc" },
			},
		},
		orderBy: { name: "asc" },
	})
}

async function updateOrganization(id: string, data: Prisma.OrganizationUpdateInput) {
	return await prisma.organization.update({
		where: { id },
		data,
		include: {
			yearData: {
				orderBy: { year: "desc" },
			},
		},
	})
}

async function deleteOrganization(id: string) {
	return await prisma.organization.delete({
		where: { id },
	})
}
