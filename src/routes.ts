import { Router } from "express"

import { router as responsesRouter } from "./modules/responses/responses.router"
import { router as surveysRouter } from "./modules/surveys/surveys.router"
import { router as evaluationsRouter } from "./modules/evaluations/evaluations.router"
const routes = Router()

routes.use("/surveys", surveysRouter)
routes.use("/responses", responsesRouter)
routes.use("/evaluations", evaluationsRouter)

export default routes
