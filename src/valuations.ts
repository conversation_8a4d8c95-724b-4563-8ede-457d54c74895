import * as xlsx from "xlsx"

type YearlyFinancials = {
	ebitda: number
	netSales: number
	operatingProfit: number
}

type Multipliers = {
	ebitda: { p25: number; p50: number; p75: number }
	netSales: { p25: number; p50: number; p75: number }
	opProfit: { p25: number; p50: number; p75: number }
}

type MVICScenarios = {
	low: number
	mid: number
	high: number
}

// Raw data row from DealStats spreadsheet
type DealStatsRow = {
	rowIndex: number
	ebitda?: number
	netSales?: number
	operatingProfit?: number
	mvicPrice?: number
	goodwillPPA?: number
}

// Processed multiplier with source row information
type MultiplierWithSource = {
	value: number
	rowIndex: number
}

const OUTLIER_SENSITIVITY = 3

function getAllMultipliersFromDealStats(filePaths: string[]): {
	multipliers: {
		ebitda: { p25: number; p50: number; p75: number }
		netSales: { p25: number; p50: number; p75: number }
		opProfit: { p25: number; p50: number; p75: number }
	}
	excludedRowIndices: Set<number>
} {
	const allRows = loadAllDealStatsRows(filePaths)

	// Extract multipliers for all metrics
	const ebitdaMultipliers = extractMultipliersWithSource(allRows, "EBITDA")
	const netSalesMultipliers = extractMultipliersWithSource(allRows, "Net Sales")
	const opProfitMultipliers = extractMultipliersWithSource(allRows, "Operating Profit")

	console.log({
		initial: `EBITDA: ${ebitdaMultipliers.length}, Net Sales: ${netSalesMultipliers.length}, Operating Profit: ${opProfitMultipliers.length}`,
	})

	// Identify outlier rows across ALL metrics - if a row is an outlier for any metric, exclude it from all
	const allOutlierRowIndices = new Set<number>()

	// Check for outliers in each metric and combine the outlier row indices
	const ebitdaOutliers = identifyOutlierRows(ebitdaMultipliers)
	const netSalesOutliers = identifyOutlierRows(netSalesMultipliers)
	const opProfitOutliers = identifyOutlierRows(opProfitMultipliers)

	// Union all outlier row indices
	ebitdaOutliers.forEach(idx => allOutlierRowIndices.add(idx))
	netSalesOutliers.forEach(idx => allOutlierRowIndices.add(idx))
	opProfitOutliers.forEach(idx => allOutlierRowIndices.add(idx))

	console.log({
		outliers: `${allOutlierRowIndices.size} total outlier rows identified across all metrics`,
		breakdown: `EBITDA: ${ebitdaOutliers.size}, Net Sales: ${netSalesOutliers.size}, Operating Profit: ${opProfitOutliers.size}`,
	})

	// Filter out outlier rows from ALL metrics
	const filteredEbitda = ebitdaMultipliers
		.filter(m => !allOutlierRowIndices.has(m.rowIndex))
		.map(m => m.value)
	const filteredNetSales = netSalesMultipliers
		.filter(m => !allOutlierRowIndices.has(m.rowIndex))
		.map(m => m.value)
	const filteredOpProfit = opProfitMultipliers
		.filter(m => !allOutlierRowIndices.has(m.rowIndex))
		.map(m => m.value)

	console.log({
		final: `EBITDA: ${filteredEbitda.length}, Net Sales: ${filteredNetSales.length}, Operating Profit: ${filteredOpProfit.length}`,
	})

	// Calculate percentiles for logging
	const ebitdaPercentiles = {
		p25: getPercentile(filteredEbitda, 25),
		p50: getPercentile(filteredEbitda, 50),
		p75: getPercentile(filteredEbitda, 75),
	}
	const netSalesPercentiles = {
		p25: getPercentile(filteredNetSales, 25),
		p50: getPercentile(filteredNetSales, 50),
		p75: getPercentile(filteredNetSales, 75),
	}
	const opProfitPercentiles = {
		p25: getPercentile(filteredOpProfit, 25),
		p50: getPercentile(filteredOpProfit, 50),
		p75: getPercentile(filteredOpProfit, 75),
	}

	console.log("📊 MVIC Multipliers by Metric:")
	console.log(
		`EBITDA Multipliers - P25: ${ebitdaPercentiles.p25.toFixed(
			2
		)}x, P50: ${ebitdaPercentiles.p50.toFixed(2)}x, P75: ${ebitdaPercentiles.p75.toFixed(2)}x`
	)
	console.log(
		`Net Sales Multipliers - P25: ${netSalesPercentiles.p25.toFixed(
			2
		)}x, P50: ${netSalesPercentiles.p50.toFixed(2)}x, P75: ${netSalesPercentiles.p75.toFixed(
			2
		)}x`
	)
	console.log(
		`Operating Profit Multipliers - P25: ${opProfitPercentiles.p25.toFixed(
			2
		)}x, P50: ${opProfitPercentiles.p50.toFixed(2)}x, P75: ${opProfitPercentiles.p75.toFixed(
			2
		)}x`
	)

	return {
		multipliers: {
			ebitda: {
				p25: getPercentile(filteredEbitda, 25),
				p50: getPercentile(filteredEbitda, 50),
				p75: getPercentile(filteredEbitda, 75),
			},
			netSales: {
				p25: getPercentile(filteredNetSales, 25),
				p50: getPercentile(filteredNetSales, 50),
				p75: getPercentile(filteredNetSales, 75),
			},
			opProfit: {
				p25: getPercentile(filteredOpProfit, 25),
				p50: getPercentile(filteredOpProfit, 50),
				p75: getPercentile(filteredOpProfit, 75),
			},
		},
		excludedRowIndices: allOutlierRowIndices,
	}
}

const dealstatsFilePaths = [
	"/Users/<USER>/Desktop/Projects/brandworth/bw-server/src/dealstats.xlsx",
	// Add more DealStats files here as needed
	// Example: "/path/to/dealstats2.xlsx",
	// Example: "/path/to/dealstats3.xlsx",
]

function formatMillions(value: number): string {
	const millions = value / 1_000_000
	return `${millions.toLocaleString("en-US", {
		minimumFractionDigits: 2,
		maximumFractionDigits: 2,
	})}M`
}

// Load and combine data from multiple DealStats spreadsheets
function loadAllDealStatsRows(filePaths: string[]): DealStatsRow[] {
	const allRows: DealStatsRow[] = []
	let globalRowIndex = 0

	for (const filePath of filePaths) {
		try {
			const workbook = xlsx.readFile(filePath)
			const sheet = workbook.Sheets[workbook.SheetNames[0]]
			const data = xlsx.utils.sheet_to_json<any>(sheet)

			for (const row of data) {
				const dealStatsRow: DealStatsRow = {
					rowIndex: globalRowIndex++,
					ebitda: parseFloat(row["EBITDA"]) || undefined,
					netSales: parseFloat(row["Net Sales"]) || undefined,
					operatingProfit: parseFloat(row["Operating Profit"]) || undefined,
					mvicPrice: parseFloat(row["MVIC Price"]) || undefined,
					goodwillPPA: parseFloat(row["Goodwill PPA"]) || undefined,
				}

				// Only include rows that have valid MVIC Price
				if (dealStatsRow.mvicPrice && dealStatsRow.mvicPrice > 0) {
					allRows.push(dealStatsRow)
				}
			}
		} catch (error) {
			console.warn(`Failed to load DealStats file ${filePath}:`, error)
		}
	}

	console.log(`Loaded ${allRows.length} valid rows from ${filePaths.length} DealStats files`)
	return allRows
}

// Extract multipliers with their source row information
function extractMultipliersWithSource(
	rows: DealStatsRow[],
	columnName: string
): MultiplierWithSource[] {
	const multipliers: MultiplierWithSource[] = []

	for (const row of rows) {
		let metricValue: number | undefined

		switch (columnName) {
			case "EBITDA":
				metricValue = row.ebitda
				break
			case "Net Sales":
				metricValue = row.netSales
				break
			case "Operating Profit":
				metricValue = row.operatingProfit
				break
			default:
				continue
		}

		if (metricValue && row.mvicPrice && metricValue > 0) {
			const multiplier = row.mvicPrice / metricValue
			multipliers.push({
				value: multiplier,
				rowIndex: row.rowIndex,
			})
		}
	}

	return multipliers
}

// Identify outlier rows using IQR method - returns set of row indices to exclude
function identifyOutlierRows(multipliersWithSource: MultiplierWithSource[]): Set<number> {
	if (multipliersWithSource.length < 4) return new Set()

	const values = multipliersWithSource.map(m => m.value)
	const sorted = [...values].sort((a, b) => a - b)
	const q1 = getPercentile(sorted, 25)
	const q3 = getPercentile(sorted, 75)
	const iqr = q3 - q1
	const lowerBound = q1 - OUTLIER_SENSITIVITY * iqr
	const upperBound = q3 + OUTLIER_SENSITIVITY * iqr

	const outlierRowIndices = new Set<number>()

	for (const multiplier of multipliersWithSource) {
		if (multiplier.value < lowerBound || multiplier.value > upperBound) {
			outlierRowIndices.add(multiplier.rowIndex)
		}
	}

	return outlierRowIndices
}

function brandPercentageOfMVIC(brandValue: number, mvic: number): string {
	return `${((brandValue / mvic) * 100).toFixed(2)}%`
}

function printMVICScenarios(mvics: number[]) {
	console.log("📈 Projected MVIC Scenarios:")
	console.log(`Low (25th percentile):  ${formatMillions(mvics[0])}`)
	console.log(`Mid (50th percentile):  ${formatMillions(mvics[1])}`)
	console.log(`High (75th percentile): ${formatMillions(mvics[2])}`)
}

function printBrandValuation(result: MVICScenarios, mvics: number[]) {
	console.log("📊 Brand Valuation Estimates:")
	console.log(`Low:  ${formatMillions(result.low)}`)
	console.log(`Mid:  ${formatMillions(result.mid)}`)
	console.log(`High: ${formatMillions(result.high)}`)

	console.log("💡 Brand Valuation as a percentage of MVIC:")
	console.log(`Low:  ${brandPercentageOfMVIC(result.low, mvics[0])}`)
	console.log(`Mid:  ${brandPercentageOfMVIC(result.mid, mvics[1])}`)
	console.log(`High: ${brandPercentageOfMVIC(result.high, mvics[2])}`)
}

function calculateProjectedMVIC(
	financials: YearlyFinancials[],
	multipliers: Multipliers
): MVICScenarios {
	// Weighted average with most recent year weighted most heavily (3, 2, 1)
	const weights = [3, 2, 1] // Most recent to oldest
	const totalWeight = weights.reduce((sum, w) => sum + w, 0)

	function weightedAvgMVIC(
		metric: keyof YearlyFinancials,
		multi: (typeof multipliers)[keyof Multipliers]
	) {
		const mvicValues = financials.map(f => f[metric]!)

		const mvicP25 =
			mvicValues.reduce((sum, value, index) => sum + value * multi.p25 * weights[index], 0) /
			totalWeight
		const mvicP50 =
			mvicValues.reduce((sum, value, index) => sum + value * multi.p50 * weights[index], 0) /
			totalWeight
		const mvicP75 =
			mvicValues.reduce((sum, value, index) => sum + value * multi.p75 * weights[index], 0) /
			totalWeight

		return { p25: mvicP25, p50: mvicP50, p75: mvicP75 }
	}

	const ebitda = weightedAvgMVIC("ebitda", multipliers.ebitda)
	const sales = weightedAvgMVIC("netSales", multipliers.netSales)
	const opProfit = weightedAvgMVIC("operatingProfit", multipliers.opProfit)

	// Simple average across the three metrics
	const avg = (arr: number[]) => arr.reduce((a, b) => a + b, 0) / arr.length

	return {
		low: avg([ebitda.p25, sales.p25, opProfit.p25]),
		mid: avg([ebitda.p50, sales.p50, opProfit.p50]),
		high: avg([ebitda.p75, sales.p75, opProfit.p75]),
	}
}

// Load goodwill multipliers from multiple DealStats files with unified outlier removal
function loadDealStatsGoodwillMultipliers(
	filePaths: string[],
	excludedRowIndices: Set<number>
): number[] {
	const allRows = loadAllDealStatsRows(filePaths)
	const goodwillMultipliersWithSource: MultiplierWithSource[] = []

	for (const row of allRows) {
		if (row.goodwillPPA && row.mvicPrice && row.mvicPrice > 0) {
			const multiplier = row.goodwillPPA / row.mvicPrice
			goodwillMultipliersWithSource.push({
				value: multiplier,
				rowIndex: row.rowIndex,
			})
		}
	}

	console.log({
		goodwillMultipliers: `${goodwillMultipliersWithSource.length} multipliers loaded from ${filePaths.length} files`,
	})

	// Use the same excluded row indices from the MVIC calculations
	const filteredMultipliers = goodwillMultipliersWithSource
		.filter(m => !excludedRowIndices.has(m.rowIndex))
		.map(m => m.value)

	console.log({
		goodwillMultipliers: `${excludedRowIndices.size} outlier rows excluded, ${filteredMultipliers.length} remaining`,
	})

	return filteredMultipliers
}

// Step 2: Percentile utility
function getPercentile(data: number[], percentile: number): number {
	const sorted = [...data].sort((a, b) => a - b)
	const index = (percentile / 100) * (sorted.length - 1)
	const lower = Math.floor(index)
	const upper = Math.ceil(index)

	if (lower === upper) return sorted[lower]
	return sorted[lower] + (sorted[upper] - sorted[lower]) * (index - lower)
}

// Calculate Brand Valuation using weighted MVIC scenarios
function calculateBrandValuation(
	mvicScenarios: MVICScenarios, // Projected MVIC scenarios (low, mid, high)
	bsi: number, // Brand Strength Index (0-1)
	goodwillPercentiles: { p25: number; p50: number; p75: number }
) {
	return {
		low: mvicScenarios.low * bsi * goodwillPercentiles.p25,
		mid: mvicScenarios.mid * bsi * goodwillPercentiles.p50,
		high: mvicScenarios.high * bsi * goodwillPercentiles.p75,
	}
}

// Main function
function main() {
	// Financial data ordered from most recent to oldest (for proper weighting)
	// The weighting scheme applies 3x weight to most recent, 2x to middle, 1x to oldest
	const revenueLast3Years = [170100000, 179700000, 168400000] // 2022, 2021, 2020
	const ebitdaLast3Years = [67300000, 67850000, 73960000] // 2022, 2021, 2020
	const netIncomeLast3Years = [45950000, 49900000, 60600000] // 2022, 2021, 2020

	const financials: YearlyFinancials[] = revenueLast3Years.map((revenue, index) => ({
		ebitda: ebitdaLast3Years[index],
		netSales: revenue,
		operatingProfit: netIncomeLast3Years[index],
	}))

	console.log("📊 Loading multipliers from DealStats files...")
	const { multipliers, excludedRowIndices } = getAllMultipliersFromDealStats(dealstatsFilePaths)

	const projectedMVICScenarios = calculateProjectedMVIC(financials, multipliers)
	const projectedMVICs = Object.values(projectedMVICScenarios)
	printMVICScenarios(projectedMVICs)

	const bsi = 0.5761

	console.log("💰 Loading goodwill multipliers...")
	const goodwillMultipliers = loadDealStatsGoodwillMultipliers(
		dealstatsFilePaths,
		excludedRowIndices
	)
	if (!goodwillMultipliers.length) {
		console.error("No valid goodwill multipliers found.")
		return
	}

	const goodwillPercentiles = {
		p25: getPercentile(goodwillMultipliers, 25),
		p50: getPercentile(goodwillMultipliers, 50),
		p75: getPercentile(goodwillMultipliers, 75),
	}

	console.log("💰 Goodwill Multiples")
	console.log(`Low:  ${goodwillPercentiles.p25.toFixed(2)}`)
	console.log(`Mid:  ${goodwillPercentiles.p50.toFixed(2)}`)
	console.log(`High: ${goodwillPercentiles.p75.toFixed(2)}`)

	const brand = calculateBrandValuation(projectedMVICScenarios, bsi, goodwillPercentiles)
	printBrandValuation(brand, projectedMVICs)
}
main()
