import * as xlsx from "xlsx"

type YearlyFinancials = {
	ebitda: number
	netSales: number
	operatingProfit: number
}

type Multipliers = {
	ebitda: { p25: number; p50: number; p75: number }
	netSales: { p25: number; p50: number; p75: number }
	opProfit: { p25: number; p50: number; p75: number }
}

type MVICScenarios = {
	low: number
	mid: number
	high: number
}

const OUTLIER_SENSITIVITY = 3

function getMultipliersFromDealStats(
	filePath: string,
	columnName: string
): { p25: number; p50: number; p75: number } {
	const values = loadMultipliers(filePath, columnName)
	console.log({ [columnName]: countOutliersIQR(values), length: values.length })
	const filteredValues = removeOutliersIQR(values)
	return {
		p25: getPercentile(filteredValues, 25),
		p50: getPercentile(filteredValues, 50),
		p75: getPercentile(filteredValues, 75),
	}
}

const dealstatsFilePath = "/Users/<USER>/Desktop/Projects/brandworth/bw-server/src/dealstats.xlsx"

function formatMillions(value: number): string {
	const millions = value / 1_000_000
	return `${millions.toLocaleString("en-US", {
		minimumFractionDigits: 2,
		maximumFractionDigits: 2,
	})}M`
}

function removeOutliersIQR(data: number[]): number[] {
	const sorted = [...data].sort((a, b) => a - b)
	const q1 = getPercentile(sorted, 25)
	const q3 = getPercentile(sorted, 75)
	const iqr = q3 - q1
	const lower = q1 - 1.5 * iqr
	const upper = q3 + 1.5 * iqr
	return data.filter(x => x >= lower && x <= upper)
}

function countOutliersIQR(data: number[]): number {
	if (data.length < 4) return 0 // Not enough data to calculate IQR

	const sorted = [...data].sort((a, b) => a - b)
	const getPercentile = (p: number): number => {
		const index = (p / 100) * (sorted.length - 1)
		const lower = Math.floor(index)
		const upper = Math.ceil(index)
		return lower === upper
			? sorted[lower]
			: sorted[lower] + (sorted[upper] - sorted[lower]) * (index - lower)
	}

	const q1 = getPercentile(25)
	const q3 = getPercentile(75)
	const iqr = q3 - q1
	const lowerBound = q1 - OUTLIER_SENSITIVITY * iqr
	const upperBound = q3 + OUTLIER_SENSITIVITY * iqr

	return data.filter(x => x < lowerBound || x > upperBound).length
}

function brandPercentageOfMVIC(brandValue: number, mvic: number): string {
	return `${((brandValue / mvic) * 100).toFixed(2)}%`
}

function printMVICScenarios(mvics: number[]) {
	console.log("📈 Projected MVIC Scenarios:")
	console.log(`Low (25th percentile):  ${formatMillions(mvics[0])}`)
	console.log(`Mid (50th percentile):  ${formatMillions(mvics[1])}`)
	console.log(`High (75th percentile): ${formatMillions(mvics[2])}`)
}

function printBrandValuation(result: MVICScenarios, mvics: number[]) {
	console.log("📊 Brand Valuation Estimates:")
	console.log(`Low:  ${formatMillions(result.low)}`)
	console.log(`Mid:  ${formatMillions(result.mid)}`)
	console.log(`High: ${formatMillions(result.high)}`)

	console.log("💡 Brand Valuation as a percentage of MVIC:")
	console.log(`Low:  ${brandPercentageOfMVIC(result.low, mvics[0])}`)
	console.log(`Mid:  ${brandPercentageOfMVIC(result.mid, mvics[1])}`)
	console.log(`High: ${brandPercentageOfMVIC(result.high, mvics[2])}`)
}

function calculateProjectedMVIC(
	financials: YearlyFinancials[],
	multipliers: Multipliers
): MVICScenarios {
	const avg = (arr: number[]) => arr.reduce((a, b) => a + b, 0) / arr.length

	function avgMVIC(
		metric: keyof YearlyFinancials,
		multi: (typeof multipliers)[keyof Multipliers]
	) {
		const mvicP25 = avg(financials.map(f => f[metric]! * multi.p25))
		const mvicP50 = avg(financials.map(f => f[metric]! * multi.p50))
		const mvicP75 = avg(financials.map(f => f[metric]! * multi.p75))
		return { p25: mvicP25, p50: mvicP50, p75: mvicP75 }
	}

	const ebitda = avgMVIC("ebitda", multipliers.ebitda)
	const sales = avgMVIC("netSales", multipliers.netSales)
	const opProfit = avgMVIC("operatingProfit", multipliers.opProfit)

	return {
		low: avg([ebitda.p25, sales.p25, opProfit.p25]),
		mid: avg([ebitda.p50, sales.p50, opProfit.p50]),
		high: avg([ebitda.p75, sales.p75, opProfit.p75]),
	}
}

// Step 1: Read DealStats file
function loadDealStatsGoodwillMultipliers(filePath: string): number[] {
	const workbook = xlsx.readFile(filePath)
	const sheet = workbook.Sheets[workbook.SheetNames[0]]
	const data = xlsx.utils.sheet_to_json<any>(sheet)

	const goodwillMultipliers: number[] = []

	for (const row of data) {
		const goodwill = parseFloat(row["Goodwill PPA"])
		const mvic = parseFloat(row["MVIC Price"])

		if (!isNaN(goodwill) && !isNaN(mvic) && mvic > 0) {
			const multiplier = goodwill / mvic
			goodwillMultipliers.push(multiplier)
		}
	}
	console.log({
		goodwillIQR: countOutliersIQR(goodwillMultipliers),
		length: goodwillMultipliers.length,
	})

	return removeOutliersIQR(goodwillMultipliers)
}

function loadMultipliers(filePath: string, columnName: string): number[] {
	const workbook = xlsx.readFile(filePath)
	const sheet = workbook.Sheets[workbook.SheetNames[0]]
	const data = xlsx.utils.sheet_to_json<any>(sheet)

	const goodwillMultipliers: number[] = []

	for (const row of data) {
		const data = parseFloat(row[columnName])
		const mvic = parseFloat(row["MVIC Price"])

		if (!isNaN(data) && !isNaN(mvic) && mvic > 0) {
			const multiplier = mvic / data
			goodwillMultipliers.push(multiplier)
		}
	}

	return goodwillMultipliers
}

// Step 2: Percentile utility
function getPercentile(data: number[], percentile: number): number {
	const sorted = [...data].sort((a, b) => a - b)
	const index = (percentile / 100) * (sorted.length - 1)
	const lower = Math.floor(index)
	const upper = Math.ceil(index)

	if (lower === upper) return sorted[lower]
	return sorted[lower] + (sorted[upper] - sorted[lower]) * (index - lower)
}

// Step 3: Calculate Brand Valuation
function calculateBrandValuation(
	mvics: number[], // 3 years of MVIC
	bsi: number, // Brand Strength Index (0-1)
	goodwillPercentiles: { p25: number; p50: number; p75: number }
) {
	const avgMVIC = mvics.reduce((sum, val) => sum + val, 0) / mvics.length

	return {
		low: avgMVIC * bsi * goodwillPercentiles.p25,
		mid: avgMVIC * bsi * goodwillPercentiles.p50,
		high: avgMVIC * bsi * goodwillPercentiles.p75,
	}
}

// Step 4: Main
function main() {
	const revenueLast3Years = [168400000, 179700000, 170100000]
	const ebitdaLast3Years = [73960000, 67850000, 67300000]
	const netIncomeLast3Years = [60600000, 49900000, 45950000]

	const financials: YearlyFinancials[] = revenueLast3Years.map((revenue, index) => ({
		ebitda: ebitdaLast3Years[index],
		netSales: revenue,
		operatingProfit: netIncomeLast3Years[index],
	}))

	const multipliers: Multipliers = {
		ebitda: getMultipliersFromDealStats(dealstatsFilePath, "EBITDA"),
		netSales: getMultipliersFromDealStats(dealstatsFilePath, "Net Sales"),
		opProfit: getMultipliersFromDealStats(dealstatsFilePath, "Operating Profit"),
	}

	const projectedMVICs = Object.values(calculateProjectedMVIC(financials, multipliers))
	printMVICScenarios(projectedMVICs)

	const bsi = 0.5761

	const goodwillMultipliers = loadDealStatsGoodwillMultipliers(dealstatsFilePath)
	if (!goodwillMultipliers.length) {
		console.error("No valid goodwill multipliers found.")
		return
	}

	const goodwillPercentiles = {
		p25: getPercentile(goodwillMultipliers, 25),
		p50: getPercentile(goodwillMultipliers, 50),
		p75: getPercentile(goodwillMultipliers, 75),
	}

	console.log("💰 Goodwill Multiples")
	console.log(`Low:  ${goodwillPercentiles.p25.toFixed(2)}`)
	console.log(`Mid:  ${goodwillPercentiles.p50.toFixed(2)}`)
	console.log(`High: ${goodwillPercentiles.p75.toFixed(2)}`)

	const brand = calculateBrandValuation(projectedMVICs, bsi, goodwillPercentiles)
	printBrandValuation(brand, projectedMVICs)
}
main()
