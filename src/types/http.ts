import { ParamsDictionary } from "express-serve-static-core"
import { IncomingHttpHeaders } from "http"
import { ParsedQs } from "qs"

export type HttpRequest = {
	body: any
	query: ParsedQs
	params: ParamsDictionary
	ip?: string
	method: string
	path: string
	headers: IncomingHttpHeaders
	cookies: string | undefined
}

export type HttpResponse = {
	statusCode: number
	body: any
	headers: {
		"Content-Type": string
	}
}
