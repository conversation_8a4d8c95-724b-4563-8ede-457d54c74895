/*
  Warnings:

  - You are about to drop the column `evaluationId` on the `Organization` table. All the data in the column will be lost.
  - You are about to drop the column `organizationDataId` on the `Organization` table. All the data in the column will be lost.
  - You are about to drop the column `value` on the `Response` table. All the data in the column will be lost.
  - You are about to drop the `OrganizationData` table. If the table is not empty, all the data it contains will be lost.
  - A unique constraint covering the columns `[selectedOptionId]` on the table `Response` will be added. If there are existing duplicate values, this will fail.
  - Made the column `surveyId` on table `Evaluation` required. This step will fail if there are existing NULL values in that column.
  - Added the required column `NAICSCode` to the `Organization` table without a default value. This is not possible if the table is not empty.
  - Added the required column `yearsInBusiness` to the `Organization` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "Evaluation" DROP CONSTRAINT "Evaluation_surveyId_fkey";

-- DropForeignKey
ALTER TABLE "OrganizationData" DROP CONSTRAINT "OrganizationData_orgId_fkey";

-- AlterTable
ALTER TABLE "Evaluation" ALTER COLUMN "surveyId" SET NOT NULL;

-- AlterTable
ALTER TABLE "Organization" DROP COLUMN "evaluationId",
DROP COLUMN "organizationDataId",
ADD COLUMN     "NAICSCode" TEXT NOT NULL,
ADD COLUMN     "yearsInBusiness" INTEGER NOT NULL;

-- AlterTable
ALTER TABLE "Response" DROP COLUMN "value",
ADD COLUMN     "selectedOptionId" TEXT;

-- DropTable
DROP TABLE "OrganizationData";

-- CreateTable
CREATE TABLE "Var" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "description" TEXT,

    CONSTRAINT "Var_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "_MultiSelectOptions" (
    "A" TEXT NOT NULL,
    "B" TEXT NOT NULL,

    CONSTRAINT "_MultiSelectOptions_AB_pkey" PRIMARY KEY ("A","B")
);

-- CreateIndex
CREATE INDEX "_MultiSelectOptions_B_index" ON "_MultiSelectOptions"("B");

-- CreateIndex
CREATE UNIQUE INDEX "Response_selectedOptionId_key" ON "Response"("selectedOptionId");

-- AddForeignKey
ALTER TABLE "Response" ADD CONSTRAINT "Response_selectedOptionId_fkey" FOREIGN KEY ("selectedOptionId") REFERENCES "QuestionOption"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Evaluation" ADD CONSTRAINT "Evaluation_surveyId_fkey" FOREIGN KEY ("surveyId") REFERENCES "Survey"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "Organization" ADD CONSTRAINT "Organization_varId_fkey" FOREIGN KEY ("varId") REFERENCES "Var"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_MultiSelectOptions" ADD CONSTRAINT "_MultiSelectOptions_A_fkey" FOREIGN KEY ("A") REFERENCES "QuestionOption"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "_MultiSelectOptions" ADD CONSTRAINT "_MultiSelectOptions_B_fkey" FOREIGN KEY ("B") REFERENCES "Response"("id") ON DELETE CASCADE ON UPDATE CASCADE;
