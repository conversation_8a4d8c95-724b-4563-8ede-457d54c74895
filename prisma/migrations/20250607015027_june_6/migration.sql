/*
  Warnings:

  - You are about to alter the column `yearsInBusiness` on the `OrganizationData` table. The data in that column could be lost. The data in that column will be cast from `BigInt` to `Integer`.
  - You are about to alter the column `year` on the `YearData` table. The data in that column could be lost. The data in that column will be cast from `BigInt` to `Integer`.

*/
-- AlterTable
ALTER TABLE "Evaluation" ALTER COLUMN "brandStrengthScore" SET DATA TYPE DOUBLE PRECISION;

-- AlterTable
ALTER TABLE "OrganizationData" ALTER COLUMN "yearsInBusiness" SET DATA TYPE INTEGER;

-- AlterTable
ALTER TABLE "QuestionOption" ADD COLUMN     "scoreIfSelected" DOUBLE PRECISION NOT NULL DEFAULT 0,
ADD COLUMN     "scoreIfUnselected" DOUBLE PRECISION NOT NULL DEFAULT 0,
ALTER COLUMN "value" SET DEFAULT 0,
ALTER COLUMN "value" SET DATA TYPE DOUBLE PRECISION;

-- AlterTable
ALTER TABLE "Response" ALTER COLUMN "value" SET DATA TYPE DOUBLE PRECISION;

-- AlterTable
ALTER TABLE "SurveyQuestion" ADD COLUMN     "group" TEXT;

-- AlterTable
ALTER TABLE "YearData" ALTER COLUMN "year" SET DATA TYPE INTEGER,
ALTER COLUMN "MVIC" SET DATA TYPE DOUBLE PRECISION,
ALTER COLUMN "EBITDA" SET DATA TYPE DOUBLE PRECISION,
ALTER COLUMN "OPM" SET DATA TYPE DOUBLE PRECISION,
ALTER COLUMN "netSales" SET DATA TYPE DOUBLE PRECISION;
