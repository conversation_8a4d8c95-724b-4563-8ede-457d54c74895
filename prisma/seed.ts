import { PrismaClient, QuestionType } from "@prisma/client"
import { SURVEY_QUESTIONS } from "./survey"

const prisma = new PrismaClient()

async function main() {
	const survey = await prisma.survey.create({ data: { name: "Evaluation Survey" } })

	const questionIdMap: Record<string, string> = {}
	const optionIdMap: Record<string, string> = {}

	for (const q of SURVEY_QUESTIONS) {
		// Create survey question
		const createdQuestion = await prisma.surveyQuestion.create({
			data: {
				surveyId: survey.id,
				question: q.question,
				type: q.type as QuestionType,
				group: q.group ?? null,
				order: SURVEY_QUESTIONS.findIndex(item => item.id === q.id) + 1,
			},
		})
		if (createdQuestion) {
			questionIdMap[q.id] = createdQuestion.id
		}

		// Create options
		if (q.type === "FREE_RESPONSE") continue
		for (const opt of q.options) {
			let createdOption

			if (q.type === "RADIO") {
				// Narrow to Radio option
				createdOption = await prisma.questionOption.create({
					data: {
						displayText: opt.displayText,
						value: (opt as { value: number }).value,
						surveyQuestionId: createdQuestion.id,
					},
				})
			} else if (q.type === "MULTISELECT") {
				// Narrow to Multiselect option
				const { scoreIfSelected, scoreIfUnselected } = opt as {
					scoreIfSelected: number
					scoreIfUnselected: number
				}

				createdOption = await prisma.questionOption.create({
					data: {
						displayText: opt.displayText,
						value: undefined,
						scoreIfSelected,
						scoreIfUnselected,
						surveyQuestionId: createdQuestion.id,
					},
				})
			}
			if (createdOption) {
				optionIdMap[`${q.id}:${opt.displayText}`] = createdOption.id
			}
		}
	}

	// Create visibility conditions
	for (const q of SURVEY_QUESTIONS) {
		if (!q.dependsOn || !q.expectedAnswer) continue

		const targetQuestionId = questionIdMap[q.id]
		const sourceQuestionId = questionIdMap[q.dependsOn]
		const expectedAnswers = q.expectedAnswer.split("+").map(s => s.trim())

		for (const answer of expectedAnswers) {
			const expectedOptionId = optionIdMap[`${q.dependsOn}:${answer}`]

			if (!expectedOptionId) {
				console.warn(
					`Skipping visibility condition for ${q.id}: expected option not found for ${q.dependsOn}:${answer}`
				)
				continue
			}

			await prisma.visibilityCondition.create({
				data: {
					sourceQuestionId,
					targetQuestionId,
					expectedOptionId,
				},
			})
		}
	}
}

main()
	.catch(e => {
		console.error(e)
		process.exit(1)
	})
	.finally(async () => {
		await prisma.$disconnect()
	})
