generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Survey {
  id          String           @id @default(uuid())
  questions   SurveyQuestion[]
  name        String           @unique
  evaluations Evaluation[]
}

model SurveyQuestion {
  id             String                @id @default(uuid())
  survey         Survey                @relation(fields: [surveyId], references: [id])
  surveyId       String
  question       String
  type           QuestionType
  shownIf        VisibilityCondition[] @relation("ShownIfQuestion")
  controls       VisibilityCondition[] @relation("ControlsVisibility")
  responses      Response[]
  questionOption QuestionOption[]
  group          String?
  order          Int
  scoringRule    Json? // Store complex scoring rules as JSON
}

model QuestionOption {
  id               String         @id @default(uuid())
  surveyQuestion   SurveyQuestion @relation(fields: [surveyQuestionId], references: [id])
  surveyQuestionId String

  displayText       String
  value             Float  @default(0)
  scoreIfSelected   Float  @default(0)
  scoreIfUnselected Float  @default(0)

  visibilityCondition VisibilityCondition[]

  // For MULTISELECT (many responses selecting this option)
  multiselectResponses Response[] @relation("MultiSelectOptions")

  // For RADIO (one response selecting this option)
  radioResponse Response? @relation("RadioSelectedOption")
}

model VisibilityCondition {
  id               String         @id @default(uuid())
  // The controlling question
  sourceQuestion   SurveyQuestion @relation("ControlsVisibility", fields: [sourceQuestionId], references: [id])
  sourceQuestionId String
  // The question that is shown if the condition matches
  targetQuestion   SurveyQuestion @relation("ShownIfQuestion", fields: [targetQuestionId], references: [id])
  targetQuestionId String
  // The value that must be selected for the target question to be shown
  expectedOption   QuestionOption @relation(fields: [expectedOptionId], references: [id])
  expectedOptionId String
}

model Response {
  id               String         @id @default(uuid())
  surveyQuestion   SurveyQuestion @relation(fields: [surveyQuestionId], references: [id])
  surveyQuestionId String

  evaluation   Evaluation @relation(fields: [evaluationId], references: [id])
  evaluationId String

  // For RADIO (only one selected)
  selectedOption   QuestionOption? @relation("RadioSelectedOption", fields: [selectedOptionId], references: [id])
  selectedOptionId String?         @unique

  // For MULTISELECT (multiple selected)
  selectedOptions QuestionOption[] @relation("MultiSelectOptions")

  // For FREE_RESPONSE (text input)
  freeResponseText String?
}

model User {
  id     String       @id @default(uuid())
  org    Organization @relation(fields: [orgId], references: [id])
  orgId  String
  role   Role         @relation(fields: [roleId], references: [id])
  roleId String
  email  String       @unique
}

model Role {
  id    String @id @default(uuid())
  name  String
  users User[]
}

model Evaluation {
  id                 String        @id @default(uuid())
  brandStrengthScore Float?
  responses          Response[]
  organization       Organization? @relation(fields: [organizationId], references: [id])
  organizationId     String?
  survey             Survey        @relation(fields: [surveyId], references: [id])
  surveyId           String
}

model Var {
  id            String         @id @default(uuid())
  name          String
  organizations Organization[]
}

model Organization {
  id              String       @id @default(uuid())
  varId           String
  evaluations     Evaluation[]
  name            String
  valuation       Float?
  yearData        YearData[]
  users           User[]
  NAICSCode       String
  yearsInBusiness Int
  var             Var          @relation(fields: [varId], references: [id])
}

model YearData {
  id       String       @id @default(uuid())
  org      Organization @relation(fields: [orgId], references: [id])
  orgId    String
  year     Int
  MVIC     Float
  EBITDA   Float
  OPM      Float
  netSales Float
}

enum QuestionType {
  RADIO
  MULTISELECT
  FREE_RESPONSE
}
